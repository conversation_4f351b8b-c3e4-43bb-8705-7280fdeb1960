<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="79" failures="73" errors="0" time="12.41">
  <testsuite name="Admin User Management API Tests" errors="0" failures="22" skipped="0" timestamp="2025-06-01T04:55:17" time="4.011" tests="26">
    <testcase classname="Admin User Management API Tests User Creation should create admin user with valid data" name="Admin User Management API Tests User Creation should create admin user with valid data" time="0.175">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:50:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should create property manager user with valid data" name="Admin User Management API Tests User Creation should create property manager user with valid data" time="0.022">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:70:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should create maintenance staff user with valid data" name="Admin User Management API Tests User Creation should create maintenance staff user with valid data" time="0.022">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:88:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should create viewer user with valid data" name="Admin User Management API Tests User Creation should create viewer user with valid data" time="0.024">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:106:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should reject user with invalid email" name="Admin User Management API Tests User Creation should reject user with invalid email" time="0.021">
      <failure>Error: expect(received).toMatch(expected)

Expected pattern: /email/i
Received string:  &quot;Validation failed&quot;
    at Object.toMatch (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:126:35)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should reject user with weak password" name="Admin User Management API Tests User Creation should reject user with weak password" time="0.026">
      <failure>Error: expect(received).toMatch(expected)

Expected pattern: /password/i
Received string:  &quot;Validation failed&quot;
    at Object.toMatch (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:141:35)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should reject user with missing required fields" name="Admin User Management API Tests User Creation should reject user with missing required fields" time="0.018">
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should reject duplicate email" name="Admin User Management API Tests User Creation should reject duplicate email" time="0.025">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:169:36)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should create user with multiple roles" name="Admin User Management API Tests User Creation should create user with multiple roles" time="0.023">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:195:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Retrieval should get all users" name="Admin User Management API Tests User Retrieval should get all users" time="0.037">
    </testcase>
    <testcase classname="Admin User Management API Tests User Retrieval should get user by ID" name="Admin User Management API Tests User Retrieval should get user by ID" time="0">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:237:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Retrieval should return 404 for non-existent user" name="Admin User Management API Tests User Retrieval should return 404 for non-existent user" time="0.597">
    </testcase>
    <testcase classname="Admin User Management API Tests User Retrieval should filter users by role" name="Admin User Management API Tests User Retrieval should filter users by role" time="0.024">
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected value: &quot;property_manager&quot;
Received array: []
    at toContain (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:272:28)
    at Array.forEach (&lt;anonymous&gt;)
    at Object.forEach (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:271:26)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Retrieval should search users by email" name="Admin User Management API Tests User Retrieval should search users by email" time="0">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;email&apos;)
    at Object.email (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:280:35)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Updates should update user details" name="Admin User Management API Tests User Updates should update user details" time="0.017">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:312:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Updates should activate/deactivate user" name="Admin User Management API Tests User Updates should activate/deactivate user" time="0.015">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:327:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Updates should reject update with invalid data" name="Admin User Management API Tests User Updates should reject update with invalid data" time="0.019">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:354:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests Role Assignment should assign single role to user" name="Admin User Management API Tests Role Assignment should assign single role to user" time="0.016">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:388:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests Role Assignment should assign multiple roles to user" name="Admin User Management API Tests Role Assignment should assign multiple roles to user" time="0.016">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:414:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests Role Assignment should add role without replacing existing" name="Admin User Management API Tests Role Assignment should add role without replacing existing" time="0.015">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:437:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests Role Assignment should remove role from user" name="Admin User Management API Tests Role Assignment should remove role from user" time="0.014">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:466:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests Permission Enforcement should enforce user creation permissions" name="Admin User Management API Tests Permission Enforcement should enforce user creation permissions" time="0.096">
      <failure>AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:494:27)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests Permission Enforcement should enforce user update permissions" name="Admin User Management API Tests Permission Enforcement should enforce user update permissions" time="0.027">
      <failure>AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:510:27)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests Permission Enforcement should enforce user deletion permissions" name="Admin User Management API Tests Permission Enforcement should enforce user deletion permissions" time="0.027">
      <failure>AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:525:27)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Deletion should delete user" name="Admin User Management API Tests User Deletion should delete user" time="0.016">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:548:47)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Deletion should return 404 when deleting non-existent user" name="Admin User Management API Tests User Deletion should return 404 when deleting non-existent user" time="0.026">
    </testcase>
    <testcase classname=" Test execution failure: could be caused by test hooks like &apos;afterAll&apos;." name=" Test execution failure: could be caused by test hooks like &apos;afterAll&apos;." time="0">
      <failure>{&quot;message&quot;:&quot;&quot;,&quot;stack&quot;:&quot;TypeError: Cannot read properties of undefined (reading &apos;email&apos;)\n    at Object.email (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\tests\\admin\\user_management.test.js:31:56)\n    at Promise.then.completed (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (&lt;anonymous&gt;)\n    at callAsyncCircusFn (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusHook (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTestsForDescribeBlock (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\run.js:154:7)\n    at _runTestsForDescribeBlock (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-runner\\build\\runTest.js:444:34)&quot;}</failure>
    </testcase>
  </testsuite>
  <testsuite name="Admin Permission Configuration API Tests" errors="0" failures="24" skipped="0" timestamp="2025-06-01T04:55:21" time="6.358" tests="24">
    <testcase classname="Admin Permission Configuration API Tests Screen Permission Configuration should configure custom screen with permissions" name="Admin Permission Configuration API Tests Screen Permission Configuration should configure custom screen with permissions" time="3.014">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:87:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Screen Permission Configuration should configure analytics screen with role restrictions" name="Admin Permission Configuration API Tests Screen Permission Configuration should configure analytics screen with role restrictions" time="0.078">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:108:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Screen Permission Configuration should reject screen with invalid data" name="Admin Permission Configuration API Tests Screen Permission Configuration should reject screen with invalid data" time="0.029">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 400
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:131:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Screen Permission Configuration should reject screen with duplicate route" name="Admin Permission Configuration API Tests Screen Permission Configuration should reject screen with duplicate route" time="0.05">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:146:36)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Screen Permission Configuration should update screen permissions" name="Admin Permission Configuration API Tests Screen Permission Configuration should update screen permissions" time="0.047">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:177:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Screen Permission Configuration should get all configured screens" name="Admin Permission Configuration API Tests Screen Permission Configuration should get all configured screens" time="0.077">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 200
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:206:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Screen Permission Configuration should filter screens by role" name="Admin Permission Configuration API Tests Screen Permission Configuration should filter screens by role" time="0.041">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 200
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:219:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Widget Permission Configuration should configure chart widget with permissions" name="Admin Permission Configuration API Tests Widget Permission Configuration should configure chart widget with permissions" time="0.074">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:241:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Widget Permission Configuration should configure table widget with role restrictions" name="Admin Permission Configuration API Tests Widget Permission Configuration should configure table widget with role restrictions" time="0.027">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:261:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Widget Permission Configuration should configure stat card widget" name="Admin Permission Configuration API Tests Widget Permission Configuration should configure stat card widget" time="0.092">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:278:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Widget Permission Configuration should reject widget with invalid configuration" name="Admin Permission Configuration API Tests Widget Permission Configuration should reject widget with invalid configuration" time="0.069">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 400
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:300:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Widget Permission Configuration should update widget permissions" name="Admin Permission Configuration API Tests Widget Permission Configuration should update widget permissions" time="0.055">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:314:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Widget Permission Configuration should get widgets by screen" name="Admin Permission Configuration API Tests Widget Permission Configuration should get widgets by screen" time="0.077">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 200
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:344:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Permission Management should create custom permission" name="Admin Permission Configuration API Tests Permission Management should create custom permission" time="0.222">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:366:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Permission Management should get all permissions" name="Admin Permission Configuration API Tests Permission Management should get all permissions" time="0.027">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:384:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Permission Management should get permissions by category" name="Admin Permission Configuration API Tests Permission Management should get permissions by category" time="0.018">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:403:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Permission Management should validate permission references in configurations" name="Admin Permission Configuration API Tests Permission Management should validate permission references in configurations" time="0.103">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 400
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:422:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Permission Enforcement should enforce screen configuration permissions" name="Admin Permission Configuration API Tests Permission Enforcement should enforce screen configuration permissions" time="0.163">
      <failure>AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:431:27)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Permission Enforcement should enforce widget configuration permissions" name="Admin Permission Configuration API Tests Permission Enforcement should enforce widget configuration permissions" time="0.047">
      <failure>AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:447:27)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Permission Enforcement should enforce permission creation permissions" name="Admin Permission Configuration API Tests Permission Enforcement should enforce permission creation permissions" time="0.027">
      <failure>AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:462:27)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Configuration Validation should validate screen accessibility for user roles" name="Admin Permission Configuration API Tests Configuration Validation should validate screen accessibility for user roles" time="0.445">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:496:46)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Configuration Validation should validate widget visibility for user permissions" name="Admin Permission Configuration API Tests Configuration Validation should validate widget visibility for user permissions" time="0.472">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:533:46)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Configuration Deletion should delete screen configuration" name="Admin Permission Configuration API Tests Configuration Deletion should delete screen configuration" time="0.044">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:562:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Permission Configuration API Tests Configuration Deletion should delete widget configuration" name="Admin Permission Configuration API Tests Configuration Deletion should delete widget configuration" time="0.062">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:593:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname=" Test execution failure: could be caused by test hooks like &apos;afterAll&apos;." name=" Test execution failure: could be caused by test hooks like &apos;afterAll&apos;." time="0">
      <failure>{&quot;message&quot;:&quot;&quot;,&quot;stack&quot;:&quot;TypeError: Cannot read properties of undefined (reading &apos;name&apos;)\n    at Object.name (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\tests\\admin\\permission_config.test.js:38:60)\n    at Promise.then.completed (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (&lt;anonymous&gt;)\n    at callAsyncCircusFn (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusHook (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTestsForDescribeBlock (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\run.js:154:7)\n    at _runTestsForDescribeBlock (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-runner\\build\\runTest.js:444:34)&quot;}</failure>
    </testcase>
  </testsuite>
  <testsuite name="Admin Role Management API Tests" errors="0" failures="27" skipped="0" timestamp="2025-06-01T04:55:27" time="1.846" tests="29">
    <testcase classname="Admin Role Management API Tests Role Creation should create custom role with valid data" name="Admin Role Management API Tests Role Creation should create custom role with valid data" time="0.001">
      <failure>TypeError: RoleFactory.createSiteCoordinator is not a function
    at Object.createSiteCoordinator (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:43:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should create facilities manager role" name="Admin Role Management API Tests Role Creation should create facilities manager role" time="0">
      <failure>TypeError: RoleFactory.createFacilitiesManager is not a function
    at Object.createFacilitiesManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:63:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should create security supervisor role" name="Admin Role Management API Tests Role Creation should create security supervisor role" time="0">
      <failure>TypeError: RoleFactory.createSecuritySupervisor is not a function
    at Object.createSecuritySupervisor (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:81:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should create escalation manager role" name="Admin Role Management API Tests Role Creation should create escalation manager role" time="0">
      <failure>TypeError: RoleFactory.createEscalationRole is not a function
    at Object.createEscalationRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:99:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should reject role with invalid name" name="Admin Role Management API Tests Role Creation should reject role with invalid name" time="0">
      <failure>TypeError: RoleFactory.createInvalid is not a function
    at Object.createInvalid (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:116:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should reject role with missing required fields" name="Admin Role Management API Tests Role Creation should reject role with missing required fields" time="0.146">
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should reject duplicate role name" name="Admin Role Management API Tests Role Creation should reject duplicate role name" time="0">
      <failure>TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:150:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should create role with minimal permissions" name="Admin Role Management API Tests Role Creation should create role with minimal permissions" time="0.001">
      <failure>TypeError: RoleFactory.createMinimalRole is not a function
    at Object.createMinimalRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:176:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should create role with maximum permissions" name="Admin Role Management API Tests Role Creation should create role with maximum permissions" time="0.001">
      <failure>TypeError: RoleFactory.createMaximalRole is not a function
    at Object.createMaximalRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:193:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Retrieval should get all roles" name="Admin Role Management API Tests Role Retrieval should get all roles" time="0">
      <failure>TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:215:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Retrieval should get role by ID" name="Admin Role Management API Tests Role Retrieval should get role by ID" time="0">
      <failure>TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:215:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Retrieval should return 404 for non-existent role" name="Admin Role Management API Tests Role Retrieval should return 404 for non-existent role" time="0">
      <failure>TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:215:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Retrieval should filter roles by type" name="Admin Role Management API Tests Role Retrieval should filter roles by type" time="0">
      <failure>TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:215:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Retrieval should search roles by name" name="Admin Role Management API Tests Role Retrieval should search roles by name" time="0">
      <failure>TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:215:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Updates should update role details" name="Admin Role Management API Tests Role Updates should update role details" time="0">
      <failure>TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:306:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Updates should add permissions to role" name="Admin Role Management API Tests Role Updates should add permissions to role" time="0">
      <failure>TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:306:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Updates should remove permissions from role" name="Admin Role Management API Tests Role Updates should remove permissions from role" time="0">
      <failure>TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:306:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Updates should reject update with invalid data" name="Admin Role Management API Tests Role Updates should reject update with invalid data" time="0">
      <failure>TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:306:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Updates should prevent updating system roles" name="Admin Role Management API Tests Role Updates should prevent updating system roles" time="0">
      <failure>TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:306:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Permission Management should get all available permissions" name="Admin Role Management API Tests Permission Management should get all available permissions" time="0.02">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:432:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Permission Management should get permissions by category" name="Admin Role Management API Tests Permission Management should get permissions by category" time="0.019">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:451:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Permission Management should validate permission assignments" name="Admin Role Management API Tests Permission Management should validate permission assignments" time="0.001">
      <failure>TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:460:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Permission Enforcement should enforce role creation permissions" name="Admin Role Management API Tests Permission Enforcement should enforce role creation permissions" time="0.073">
      <failure>AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:479:27)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Permission Enforcement should enforce role update permissions" name="Admin Role Management API Tests Permission Enforcement should enforce role update permissions" time="0.024">
      <failure>AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:495:27)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Permission Enforcement should enforce role deletion permissions" name="Admin Role Management API Tests Permission Enforcement should enforce role deletion permissions" time="0.027">
      <failure>AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:510:27)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Deletion should delete custom role" name="Admin Role Management API Tests Role Deletion should delete custom role" time="0">
      <failure>TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:526:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Deletion should prevent deletion of system roles" name="Admin Role Management API Tests Role Deletion should prevent deletion of system roles" time="0.019">
      <failure>TypeError: rolesResponse.data.data.find is not a function
    at Object.find (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:563:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Deletion should prevent deletion of role assigned to users" name="Admin Role Management API Tests Role Deletion should prevent deletion of role assigned to users" time="0.019">
      <failure>TypeError: rolesResponse.data.data.find is not a function
    at Object.find (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:586:59)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Deletion should return 404 when deleting non-existent role" name="Admin Role Management API Tests Role Deletion should return 404 when deleting non-existent role" time="0.76">
    </testcase>
  </testsuite>
</testsuites>