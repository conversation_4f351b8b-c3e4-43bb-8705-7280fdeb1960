import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const createThresholdSchema = Joi.object({
  functional_area: Joi.string().required(),
  property_type: Joi.string().valid('residential', 'office', 'construction_site').required(),
  metric_name: Joi.string().required(),
  threshold_type: Joi.string().valid('min', 'max', 'range').required(),
  min_value: Joi.number().when('threshold_type', {
    is: Joi.valid('min', 'range'),
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  max_value: Joi.number().when('threshold_type', {
    is: Joi.valid('max', 'range'),
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  unit: Joi.string().optional(),
  severity: Joi.string().valid('low', 'medium', 'high', 'critical').default('medium'),
  is_enabled: Joi.boolean().default(true),
});

const bulkCreateThresholdsSchema = Joi.object({
  thresholds: Joi.array().items(createThresholdSchema).min(1).required(),
});

const thresholdUpdateSchema = Joi.object({
  id: Joi.string().uuid().required(),
  functional_area: Joi.string().optional(),
  property_type: Joi.string().valid('residential', 'office', 'construction_site').optional(),
  metric_name: Joi.string().optional(),
  threshold_type: Joi.string().valid('min', 'max', 'range').optional(),
  min_value: Joi.number().optional(),
  max_value: Joi.number().optional(),
  unit: Joi.string().optional(),
  severity: Joi.string().valid('low', 'medium', 'high', 'critical').optional(),
  is_enabled: Joi.boolean().optional(),
});

const bulkUpdateThresholdsSchema = Joi.object({
  thresholds: Joi.array().items(thresholdUpdateSchema).min(1).required(),
});

async function bulkCreateThresholdsHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(bulkCreateThresholdsSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { thresholds } = validation.data;

    // Create thresholds in bulk
    const createdThresholds = [];
    const errors = [];

    for (const thresholdData of thresholds) {
      try {
        const threshold = await prisma.thresholdConfig.create({
          data: {
            functionalArea: thresholdData.functional_area,
            propertyType: thresholdData.property_type,
            metricName: thresholdData.metric_name,
            thresholdType: thresholdData.threshold_type,
            minValue: thresholdData.min_value,
            maxValue: thresholdData.max_value,
            unit: thresholdData.unit,
            severity: thresholdData.severity,
            isEnabled: thresholdData.is_enabled,
            createdBy: currentUser.id,
          },
        });

        createdThresholds.push({
          id: threshold.id,
          functional_area: threshold.functionalArea,
          property_type: threshold.propertyType,
          metric_name: threshold.metricName,
          threshold_type: threshold.thresholdType,
          min_value: threshold.minValue,
          max_value: threshold.maxValue,
          unit: threshold.unit,
          severity: threshold.severity,
          is_enabled: threshold.isEnabled,
          created_at: threshold.createdAt,
        });
      } catch (error: any) {
        errors.push({
          threshold: thresholdData,
          error: error.message,
        });
      }
    }

    return Response.json(
      createApiResponse({
        created: createdThresholds,
        errors,
        summary: {
          total: thresholds.length,
          successful: createdThresholds.length,
          failed: errors.length,
        },
      }),
      {
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to bulk create thresholds');
  }
}

async function bulkUpdateThresholdsHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(bulkUpdateThresholdsSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { thresholds } = validation.data;

    // Update thresholds in bulk
    const updatedThresholds = [];
    const errors = [];

    for (const thresholdData of thresholds) {
      try {
        const updateData: any = {};
        
        if (thresholdData.functional_area !== undefined) updateData.functionalArea = thresholdData.functional_area;
        if (thresholdData.property_type !== undefined) updateData.propertyType = thresholdData.property_type;
        if (thresholdData.metric_name !== undefined) updateData.metricName = thresholdData.metric_name;
        if (thresholdData.threshold_type !== undefined) updateData.thresholdType = thresholdData.threshold_type;
        if (thresholdData.min_value !== undefined) updateData.minValue = thresholdData.min_value;
        if (thresholdData.max_value !== undefined) updateData.maxValue = thresholdData.max_value;
        if (thresholdData.unit !== undefined) updateData.unit = thresholdData.unit;
        if (thresholdData.severity !== undefined) updateData.severity = thresholdData.severity;
        if (thresholdData.is_enabled !== undefined) updateData.isEnabled = thresholdData.is_enabled;

        const threshold = await prisma.thresholdConfig.update({
          where: { id: thresholdData.id },
          data: updateData,
        });

        updatedThresholds.push({
          id: threshold.id,
          functional_area: threshold.functionalArea,
          property_type: threshold.propertyType,
          metric_name: threshold.metricName,
          threshold_type: threshold.thresholdType,
          min_value: threshold.minValue,
          max_value: threshold.maxValue,
          unit: threshold.unit,
          severity: threshold.severity,
          is_enabled: threshold.isEnabled,
          updated_at: threshold.updatedAt,
        });
      } catch (error: any) {
        errors.push({
          threshold_id: thresholdData.id,
          error: error.message,
        });
      }
    }

    return Response.json(
      createApiResponse({
        updated: updatedThresholds,
        errors,
        summary: {
          total: thresholds.length,
          successful: updatedThresholds.length,
          failed: errors.length,
        },
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to bulk update thresholds');
  }
}

// POST /api/thresholds/bulk - Bulk create thresholds
export const POST = requireRole(['admin'])(bulkCreateThresholdsHandler);

// PUT /api/thresholds/bulk - Bulk update thresholds
export const PUT = requireRole(['admin'])(bulkUpdateThresholdsHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
