const AuthHelper = require('../../src/helpers/auth_helper');
const { RoleFactory } = require('../../src/factories/role_factory');
const config = require('../../src/config/test.config');

describe('Admin Role Management API Tests', () => {
  let adminToken;
  let testRoles = [];

  beforeAll(async () => {
    console.log('🔧 Setting up Admin Role Management API tests...');

    try {
      adminToken = await AuthHelper.getAdminToken();
      console.log('✅ Admin authentication successful');
    } catch (error) {
      console.error('❌ Failed to authenticate admin user:', error.message);
      throw error;
    }
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up test roles...');

    for (const role of testRoles) {
      try {
        await AuthHelper.makeAuthenticatedRequest(
          adminToken,
          'DELETE',
          `/roles/${role.id}`
        );
        console.log(`✅ Deleted test role: ${role.name}`);
      } catch (error) {
        console.warn(`⚠️ Failed to cleanup role ${role.name}:`, error.message);
      }
    }

    testRoles = [];
    console.log('✅ Cleanup completed');
  });

  describe('Role Creation', () => {
    test('should create custom role with valid data', async () => {
      const roleData = RoleFactory.createSiteCoordinator();

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/roles',
        roleData
      );

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.name).toBe(roleData.name);
      expect(response.data.data.description).toBe(roleData.description);
      expect(response.data.data.permissions).toEqual(expect.arrayContaining(roleData.permissions));
      expect(response.data.data.isSystemRole).toBe(false);

      testRoles.push(response.data.data);
    });

    test('should create facilities manager role', async () => {
      const roleData = RoleFactory.createFacilitiesManager();

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/roles',
        roleData
      );

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.name).toBe(roleData.name);
      expect(response.data.data.permissions).toEqual(expect.arrayContaining(roleData.permissions));

      testRoles.push(response.data.data);
    });

    test('should create security supervisor role', async () => {
      const roleData = RoleFactory.createSecuritySupervisor();

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/roles',
        roleData
      );

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.name).toBe(roleData.name);
      expect(response.data.data.permissions).toEqual(expect.arrayContaining(roleData.permissions));

      testRoles.push(response.data.data);
    });

    test('should create escalation manager role', async () => {
      const roleData = RoleFactory.createEscalationRole();

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/roles',
        roleData
      );

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.permissions).toContain('maintenance.escalate');

      testRoles.push(response.data.data);
    });

    test('should reject role with invalid name', async () => {
      const roleData = RoleFactory.createInvalid();

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/roles',
        roleData
      );

      expect(response.status).toBe(400);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toMatch(/name/i);
    });

    test('should reject role with missing required fields', async () => {
      const roleData = {
        // Missing name and description
        permissions: ['dashboard.view'],
        isSystemRole: false
      };

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/roles',
        roleData
      );

      expect(response.status).toBe(400);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toBeDefined();
    });

    test('should reject duplicate role name', async () => {
      const roleData = RoleFactory.createCustomRole();

      // Create first role
      const firstResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/roles',
        roleData
      );
      expect(firstResponse.status).toBe(201);
      testRoles.push(firstResponse.data.data);

      // Try to create duplicate
      const duplicateResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/roles',
        roleData
      );

      expect(duplicateResponse.status).toBe(409);
      expect(duplicateResponse.data.success).toBe(false);
      expect(duplicateResponse.data.error).toMatch(/already exists|duplicate/i);
    });

    test('should create role with minimal permissions', async () => {
      const roleData = RoleFactory.createMinimalRole();

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/roles',
        roleData
      );

      expect(response.status).toBe(201);
      expect(response.data.data.permissions).toHaveLength(1);
      expect(response.data.data.permissions).toContain('dashboard.view');

      testRoles.push(response.data.data);
    });

    test('should create role with maximum permissions', async () => {
      const roleData = RoleFactory.createMaximalRole();

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/roles',
        roleData
      );

      expect(response.status).toBe(201);
      expect(response.data.data.permissions.length).toBeGreaterThan(10);
      expect(response.data.data.permissions).not.toContain('users.create'); // Should not have admin permissions

      testRoles.push(response.data.data);
    });
  });

  describe('Role Retrieval', () => {
    let testRole;

    beforeAll(async () => {
      // Create a test role for retrieval tests
      const roleData = RoleFactory.createCustomRole();
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/roles',
        roleData
      );
      testRole = response.data.data;
      testRoles.push(testRole);
    });

    test('should get all roles', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/roles'
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(Array.isArray(response.data.data)).toBe(true);
      expect(response.data.data.length).toBeGreaterThan(0);

      // Should include system roles
      const roleNames = response.data.data.map(role => role.name);
      expect(roleNames).toContain('admin');
      expect(roleNames).toContain('property_manager');
    });

    test('should get role by ID', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        `/roles/${testRole.id}`
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.id).toBe(testRole.id);
      expect(response.data.data.name).toBe(testRole.name);
    });

    test('should return 404 for non-existent role', async () => {
      const nonExistentId = '99999999-9999-9999-9999-999999999999';

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        `/roles/${nonExistentId}`
      );

      expect(response.status).toBe(404);
      expect(response.data.success).toBe(false);
    });

    test('should filter roles by type', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/roles?type=custom'
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(Array.isArray(response.data.data)).toBe(true);

      // All returned roles should be custom (non-system) roles
      response.data.data.forEach(role => {
        expect(role.isSystemRole).toBe(false);
      });
    });

    test('should search roles by name', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        `/roles?search=${testRole.name}`
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(Array.isArray(response.data.data)).toBe(true);
      expect(response.data.data.some(role => role.name === testRole.name)).toBe(true);
    });
  });

  describe('Role Updates', () => {
    let testRole;

    beforeEach(async () => {
      // Create a fresh test role for each update test
      const roleData = RoleFactory.createCustomRole();
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/roles',
        roleData
      );
      testRole = response.data.data;
      testRoles.push(testRole);
    });

    test('should update role details', async () => {
      const updateData = {
        description: 'Updated role description',
        permissions: [...testRole.permissions, 'reports.export']
      };

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'PUT',
        `/roles/${testRole.id}`,
        updateData
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.description).toBe(updateData.description);
      expect(response.data.data.permissions).toEqual(expect.arrayContaining(updateData.permissions));
    });

    test('should add permissions to role', async () => {
      const newPermissions = ['fuel.create', 'fuel.update'];
      const updateData = {
        permissions: [...testRole.permissions, ...newPermissions]
      };

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'PUT',
        `/roles/${testRole.id}`,
        updateData
      );

      expect(response.status).toBe(200);
      expect(response.data.data.permissions).toEqual(expect.arrayContaining(newPermissions));
    });

    test('should remove permissions from role', async () => {
      // First add some permissions
      const initialPermissions = [...testRole.permissions, 'fuel.create', 'fuel.update'];
      await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'PUT',
        `/roles/${testRole.id}`,
        { permissions: initialPermissions }
      );

      // Then remove one permission
      const updatedPermissions = initialPermissions.filter(p => p !== 'fuel.create');
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'PUT',
        `/roles/${testRole.id}`,
        { permissions: updatedPermissions }
      );

      expect(response.status).toBe(200);
      expect(response.data.data.permissions).not.toContain('fuel.create');
      expect(response.data.data.permissions).toContain('fuel.update');
    });

    test('should reject update with invalid data', async () => {
      const invalidUpdateData = {
        name: '' // Invalid: empty name
      };

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'PUT',
        `/roles/${testRole.id}`,
        invalidUpdateData
      );

      expect(response.status).toBe(400);
      expect(response.data.success).toBe(false);
    });

    test('should prevent updating system roles', async () => {
      // Try to update admin role
      const updateData = {
        description: 'Modified admin role'
      };

      // First get admin role ID
      const rolesResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/roles?search=admin'
      );

      const adminRole = rolesResponse.data.data.find(role => role.name === 'admin');
      expect(adminRole).toBeDefined();

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'PUT',
        `/roles/${adminRole.id}`,
        updateData
      );

      expect(response.status).toBe(403);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toMatch(/system role|cannot be modified/i);
    });
  });

  describe('Permission Management', () => {
    test('should get all available permissions', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/permissions'
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(Array.isArray(response.data.data)).toBe(true);
      expect(response.data.data.length).toBeGreaterThan(0);

      // Should include standard permissions
      const permissionNames = response.data.data.map(perm => perm.name);
      expect(permissionNames).toContain('properties.read');
      expect(permissionNames).toContain('maintenance.read');
      expect(permissionNames).toContain('dashboard.view');
    });

    test('should get permissions by category', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/permissions?category=properties'
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(Array.isArray(response.data.data)).toBe(true);

      // All returned permissions should be in properties category
      response.data.data.forEach(permission => {
        expect(permission.category).toBe('properties');
      });
    });

    test('should validate permission assignments', async () => {
      const roleData = RoleFactory.createCustomRole();
      roleData.permissions = ['invalid.permission', 'nonexistent.action'];

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/roles',
        roleData
      );

      expect(response.status).toBe(400);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toMatch(/invalid permission|permission not found/i);
    });
  });

  describe('Permission Enforcement', () => {
    test('should enforce role creation permissions', async () => {
      // Get token for non-admin user
      const viewerToken = await AuthHelper.getViewerToken();
      const roleData = RoleFactory.createCustomRole();

      const response = await AuthHelper.makeAuthenticatedRequest(
        viewerToken,
        'POST',
        '/roles',
        roleData
      );

      expect(response.status).toBe(403);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toMatch(/permission|unauthorized|forbidden/i);
    });

    test('should enforce role update permissions', async () => {
      const viewerToken = await AuthHelper.getViewerToken();
      const updateData = { description: 'Unauthorized update' };

      const response = await AuthHelper.makeAuthenticatedRequest(
        viewerToken,
        'PUT',
        `/roles/${testRoles[0]?.id || 'dummy-id'}`,
        updateData
      );

      expect(response.status).toBe(403);
      expect(response.data.success).toBe(false);
    });

    test('should enforce role deletion permissions', async () => {
      const viewerToken = await AuthHelper.getViewerToken();

      const response = await AuthHelper.makeAuthenticatedRequest(
        viewerToken,
        'DELETE',
        `/roles/${testRoles[0]?.id || 'dummy-id'}`
      );

      expect(response.status).toBe(403);
      expect(response.data.success).toBe(false);
    });
  });

  describe('Role Deletion', () => {
    test('should delete custom role', async () => {
      // Create role to delete
      const roleData = RoleFactory.createCustomRole();
      const createResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/roles',
        roleData
      );
      const roleId = createResponse.data.data.id;

      // Delete role
      const deleteResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'DELETE',
        `/roles/${roleId}`
      );

      expect(deleteResponse.status).toBe(200);
      expect(deleteResponse.data.success).toBe(true);

      // Verify role is deleted
      const getResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        `/roles/${roleId}`
      );

      expect(getResponse.status).toBe(404);
    });

    test('should prevent deletion of system roles', async () => {
      // Get admin role ID
      const rolesResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/roles?search=admin'
      );

      const adminRole = rolesResponse.data.data.find(role => role.name === 'admin');
      expect(adminRole).toBeDefined();

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'DELETE',
        `/roles/${adminRole.id}`
      );

      expect(response.status).toBe(403);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toMatch(/system role|cannot be deleted/i);
    });

    test('should prevent deletion of role assigned to users', async () => {
      // This test would require creating a user with the role first
      // For now, we'll test with an existing system role that has users
      const rolesResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/roles?search=property_manager'
      );

      const propertyManagerRole = rolesResponse.data.data.find(role => role.name === 'property_manager');
      expect(propertyManagerRole).toBeDefined();

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'DELETE',
        `/roles/${propertyManagerRole.id}`
      );

      expect(response.status).toBe(409);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toMatch(/role is assigned|users assigned/i);
    });

    test('should return 404 when deleting non-existent role', async () => {
      const nonExistentId = '99999999-9999-9999-9999-999999999999';

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'DELETE',
        `/roles/${nonExistentId}`
      );

      expect(response.status).toBe(404);
      expect(response.data.success).toBe(false);
    });
  });
});
