{"openapi": "3.0.3", "info": {"title": "SRSR API - Authentication", "version": "1.0.0"}, "paths": {"/api/auth/login": {"post": {"summary": "User login", "description": "Authenticate user with email/username/phone and password. Supports multi-field login for flexible authentication.", "tags": ["Authentication"], "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/LoginRequest"}, {"$ref": "#/components/schemas/LegacyLoginRequest"}]}, "examples": {"multi_field_login": {"summary": "Multi-field login (recommended)", "value": {"identifier": "<EMAIL>", "password": "admin123", "login_type": "email"}}, "username_login": {"summary": "<PERSON>rname login", "value": {"identifier": "admin_user", "password": "admin123", "login_type": "username"}}, "phone_login": {"summary": "Phone login", "value": {"identifier": "+**********", "password": "admin123", "login_type": "phone"}}, "legacy_login": {"summary": "Legacy email login (backward compatibility)", "value": {"email": "<EMAIL>", "password": "admin123"}}}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}, "example": {"success": true, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "user": {"id": "123e4567-e89b-12d3-a456-************", "email": "<EMAIL>", "full_name": "System Administrator", "roles": ["admin"], "permissions": ["*"]}}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"description": "Invalid credentials or account deactivated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "examples": {"invalid_credentials": {"summary": "Invalid credentials", "value": {"success": false, "error": "Invalid credentials", "code": "INVALID_CREDENTIALS"}}, "account_deactivated": {"summary": "Account deactivated", "value": {"success": false, "error": "Account is deactivated", "code": "ACCOUNT_DEACTIVATED"}}}}}}}}}, "/api/auth/register": {"post": {"summary": "Register new user", "description": "Register a new user account. Only admin users can register new users.", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}, "example": {"email": "<EMAIL>", "username": "newuser", "password": "securepassword123", "full_name": "New User", "phone": "+**********"}}}}, "responses": {"201": {"description": "User registered successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterResponse"}, "example": {"success": true, "data": {"message": "User registered successfully", "user": {"id": "123e4567-e89b-12d3-a456-************", "email": "<EMAIL>", "full_name": "New User", "is_active": true, "roles": []}}}}}}, "400": {"description": "Validation error or user already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "examples": {"validation_error": {"summary": "Validation error", "value": {"success": false, "error": "Validation failed", "code": "VALIDATION_ERROR"}}, "user_exists": {"summary": "User already exists", "value": {"success": false, "error": "User with this email already exists", "code": "USER_EXISTS"}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "403": {"$ref": "#/components/responses/ForbiddenError"}}}}, "/api/auth/me": {"get": {"summary": "Get current user profile", "description": "Get the profile information of the currently authenticated user", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "User profile retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/User"}}}]}, "example": {"success": true, "data": {"id": "123e4567-e89b-12d3-a456-************", "email": "<EMAIL>", "username": "admin", "full_name": "System Administrator", "phone": "+**********", "is_active": true, "roles": ["admin"], "permissions": ["*"], "created_at": "2024-01-01T00:00:00Z"}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"schemas": {"LoginRequest": {"type": "object", "properties": {"identifier": {"type": "string", "description": "Email, username, or phone number"}, "password": {"type": "string", "minLength": 6, "description": "User password"}, "login_type": {"type": "string", "enum": ["email", "username", "phone"], "description": "Optional login type hint for faster processing"}}, "required": ["identifier", "password"]}, "LegacyLoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "User email address"}, "password": {"type": "string", "minLength": 6, "description": "User password"}}, "required": ["email", "password"]}, "LoginResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "token": {"type": "string", "description": "JWT authentication token"}, "user": {"$ref": "#/components/schemas/User"}}, "required": ["success", "token", "user"]}, "RegisterRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "User email address"}, "username": {"type": "string", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z0-9_-]+$", "description": "Unique username"}, "password": {"type": "string", "minLength": 6, "description": "User password"}, "full_name": {"type": "string", "minLength": 2, "description": "User's full name"}, "phone": {"type": "string", "pattern": "^[+]?[1-9]\\d{1,14}$", "description": "User's phone number"}}, "required": ["email", "password", "full_name"]}, "RegisterResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"message": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}}}, "required": ["success", "data"]}}}}