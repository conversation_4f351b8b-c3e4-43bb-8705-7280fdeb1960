<html><head><meta charset="utf-8"/><title>SRSR Admin API Test Report</title><style type="text/css">html,
body {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 1rem;
  margin: 0;
  padding: 0;
  color: #333;
}
body {
  padding: 2rem 1rem;
  font-size: 0.85rem;
}
.jesthtml-content {
  margin: 0 auto;
  max-width: 70rem;
}
header {
  display: flex;
  align-items: center;
}
#title {
  margin: 0;
  flex-grow: 1;
}
#logo {
  height: 4rem;
}
#timestamp {
  color: #777;
  margin-top: 0.5rem;
}

/** SUMMARY */
#summary {
  color: #333;
  margin: 2rem 0;
  display: flex;
  font-family: monospace;
  font-size: 1rem;
}
#summary > div {
  margin-right: 2rem;
  background: #eee;
  padding: 1rem;
  min-width: 15rem;
}
#summary > div:last-child {
  margin-right: 0;
}
@media only screen and (max-width: 720px) {
  #summary {
    flex-direction: column;
  }
  #summary > div {
    margin-right: 0;
    margin-top: 2rem;
  }
  #summary > div:first-child {
    margin-top: 0;
  }
}

.summary-total {
  font-weight: bold;
  margin-bottom: 0.5rem;
}
.summary-passed {
  color: #4f8a10;
  border-left: 0.4rem solid #4f8a10;
  padding-left: 0.5rem;
}
.summary-failed,
.summary-obsolete-snapshots {
  color: #d8000c;
  border-left: 0.4rem solid #d8000c;
  padding-left: 0.5rem;
}
.summary-pending {
  color: #9f6000;
  border-left: 0.4rem solid #9f6000;
  padding-left: 0.5rem;
}
.summary-empty {
  color: #999;
  border-left: 0.4rem solid #999;
}

.test-result {
  padding: 1rem;
  margin-bottom: 0.25rem;
}
.test-result:last-child {
  border: 0;
}
.test-result.passed {
  background-color: #dff2bf;
  color: #4f8a10;
}
.test-result.failed {
  background-color: #ffbaba;
  color: #d8000c;
}
.test-result.pending {
  background-color: #ffdf61;
  color: #9f6000;
}

.test-info {
  display: flex;
  justify-content: space-between;
}
.test-suitename {
  width: 20%;
  text-align: left;
  font-weight: bold;
  word-break: break-word;
}
.test-title {
  width: 40%;
  text-align: left;
  font-style: italic;
}
.test-status {
  width: 20%;
  text-align: right;
}
.test-duration {
  width: 10%;
  text-align: right;
  font-size: 0.75rem;
}

.failureMessages {
  padding: 0 1rem;
  margin-top: 1rem;
  border-top: 1px dashed #d8000c;
}
.failureMessages.suiteFailure {
  border-top: none;
}
.failureMsg {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}

.suite-container {
  margin-bottom: 2rem;
}
.suite-container > input[type="checkbox"] {
  position: absolute;
  left: -100vw;
}
.suite-container label {
  display: block;
}
.suite-container .suite-tests {
  overflow-y: hidden;
  height: 0;
}
.suite-container > input[type="checkbox"]:checked ~ .suite-tests {
  height: auto;
  overflow: visible;
}
.suite-info {
  padding: 1rem;
  background-color: #eee;
  color: #777;
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
}
.suite-info:hover {
  background-color: #ddd;
  cursor: pointer;
}
.suite-info .suite-path {
  word-break: break-all;
  flex-grow: 1;
  font-family: monospace;
  font-size: 1rem;
}
.suite-info .suite-time {
  margin-left: 0.5rem;
  padding: 0.2rem 0.3rem;
  font-size: 0.75rem;
}
.suite-info .suite-time.warn {
  background-color: #d8000c;
  color: #fff;
}
.suite-info:before {
  content: "\2303";
  display: inline-block;
  margin-right: 0.5rem;
  transform: rotate(0deg);
}
.suite-container > input[type="checkbox"]:checked ~ label .suite-info:before {
  transform: rotate(180deg);
}

/* CONSOLE LOGS */
.suite-consolelog {
  margin-bottom: 0.25rem;
  padding: 1rem;
  background-color: #efefef;
}
.suite-consolelog-header {
  font-weight: bold;
}
.suite-consolelog-item {
  padding: 0.5rem;
}
.suite-consolelog-item pre {
  margin: 0.5rem 0;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}
.suite-consolelog-item-origin {
  color: #777;
  font-weight: bold;
}
.suite-consolelog-item-message {
  color: #000;
  font-size: 1rem;
  padding: 0 0.5rem;
}

/* OBSOLETE SNAPSHOTS */
.suite-obsolete-snapshots {
  margin-bottom: 0.25rem;
  padding: 1rem;
  background-color: #ffbaba;
  color: #d8000c;
}
.suite-obsolete-snapshots-header {
  font-weight: bold;
}
.suite-obsolete-snapshots-item {
  padding: 0.5rem;
}
.suite-obsolete-snapshots-item pre {
  margin: 0.5rem 0;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}
.suite-obsolete-snapshots-item-message {
  color: #000;
  font-size: 1rem;
  padding: 0 0.5rem;
}
</style></head><body><div class="jesthtml-content"><header><h1 id="title">SRSR Admin API Test Report</h1></header><div id="metadata-container"><div id="timestamp">Started: 2025-06-01 10:25:17</div><div id="summary"><div id="suite-summary"><div class="summary-total">Suites (3)</div><div class="summary-passed  summary-empty">0 passed</div><div class="summary-failed ">3 failed</div><div class="summary-pending  summary-empty">0 pending</div></div><div id="test-summary"><div class="summary-total">Tests (79)</div><div class="summary-passed ">6 passed</div><div class="summary-failed ">73 failed</div><div class="summary-pending  summary-empty">0 pending</div></div></div></div><div id="suite-1" class="suite-container"><input id="collapsible-0" type="checkbox" class="toggle" checked="checked"/><label for="collapsible-0"><div class="suite-info"><div class="suite-path">D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js</div><div class="suite-time">4.011s</div></div></label><div class="suite-tests"><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should create admin user with valid data</div><div class="test-status">failed</div><div class="test-duration">0.175s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:50:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should create property manager user with valid data</div><div class="test-status">failed</div><div class="test-duration">0.022s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:70:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should create maintenance staff user with valid data</div><div class="test-status">failed</div><div class="test-duration">0.022s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:88:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should create viewer user with valid data</div><div class="test-status">failed</div><div class="test-duration">0.024s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:106:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should reject user with invalid email</div><div class="test-status">failed</div><div class="test-duration">0.021s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toMatch(expected)

Expected pattern: /email/i
Received string:  "Validation failed"
    at Object.toMatch (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:126:35)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should reject user with weak password</div><div class="test-status">failed</div><div class="test-duration">0.026s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toMatch(expected)

Expected pattern: /password/i
Received string:  "Validation failed"
    at Object.toMatch (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:141:35)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result passed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should reject user with missing required fields</div><div class="test-status">passed</div><div class="test-duration">0.018s</div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should reject duplicate email</div><div class="test-status">failed</div><div class="test-duration">0.025s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:169:36)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should create user with multiple roles</div><div class="test-status">failed</div><div class="test-duration">0.023s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:195:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result passed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Retrieval</div><div class="test-title">should get all users</div><div class="test-status">passed</div><div class="test-duration">0.037s</div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Retrieval</div><div class="test-title">should get user by ID</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:237:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result passed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Retrieval</div><div class="test-title">should return 404 for non-existent user</div><div class="test-status">passed</div><div class="test-duration">0.597s</div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Retrieval</div><div class="test-title">should filter users by role</div><div class="test-status">failed</div><div class="test-duration">0.024s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toContain(expected) // indexOf

Expected value: "property_manager"
Received array: []
    at toContain (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:272:28)
    at Array.forEach (&lt;anonymous&gt;)
    at Object.forEach (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:271:26)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Retrieval</div><div class="test-title">should search users by email</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'email')
    at Object.email (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:280:35)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Updates</div><div class="test-title">should update user details</div><div class="test-status">failed</div><div class="test-duration">0.017s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:312:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Updates</div><div class="test-title">should activate/deactivate user</div><div class="test-status">failed</div><div class="test-duration">0.015s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:327:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Updates</div><div class="test-title">should reject update with invalid data</div><div class="test-status">failed</div><div class="test-duration">0.019s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:354:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; Role Assignment</div><div class="test-title">should assign single role to user</div><div class="test-status">failed</div><div class="test-duration">0.016s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:388:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; Role Assignment</div><div class="test-title">should assign multiple roles to user</div><div class="test-status">failed</div><div class="test-duration">0.016s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:414:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; Role Assignment</div><div class="test-title">should add role without replacing existing</div><div class="test-status">failed</div><div class="test-duration">0.015s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:437:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; Role Assignment</div><div class="test-title">should remove role from user</div><div class="test-status">failed</div><div class="test-duration">0.014s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:466:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce user creation permissions</div><div class="test-status">failed</div><div class="test-duration">0.096s</div></div><div class="failureMessages"> <pre class="failureMsg">AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:494:27)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce user update permissions</div><div class="test-status">failed</div><div class="test-duration">0.027s</div></div><div class="failureMessages"> <pre class="failureMsg">AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:510:27)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce user deletion permissions</div><div class="test-status">failed</div><div class="test-duration">0.027s</div></div><div class="failureMessages"> <pre class="failureMsg">AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:525:27)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Deletion</div><div class="test-title">should delete user</div><div class="test-status">failed</div><div class="test-duration">0.016s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:548:47)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result passed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Deletion</div><div class="test-title">should return 404 when deleting non-existent user</div><div class="test-status">passed</div><div class="test-duration">0.026s</div></div></div></div></div><div id="suite-2" class="suite-container"><input id="collapsible-1" type="checkbox" class="toggle" checked="checked"/><label for="collapsible-1"><div class="suite-info"><div class="suite-path">D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js</div><div class="suite-time warn">6.358s</div></div></label><div class="suite-tests"><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Screen Permission Configuration</div><div class="test-title">should configure custom screen with permissions</div><div class="test-status">failed</div><div class="test-duration">3.014s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:87:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Screen Permission Configuration</div><div class="test-title">should configure analytics screen with role restrictions</div><div class="test-status">failed</div><div class="test-duration">0.078s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:108:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Screen Permission Configuration</div><div class="test-title">should reject screen with invalid data</div><div class="test-status">failed</div><div class="test-duration">0.029s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 400
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:131:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Screen Permission Configuration</div><div class="test-title">should reject screen with duplicate route</div><div class="test-status">failed</div><div class="test-duration">0.05s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:146:36)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Screen Permission Configuration</div><div class="test-title">should update screen permissions</div><div class="test-status">failed</div><div class="test-duration">0.047s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:177:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Screen Permission Configuration</div><div class="test-title">should get all configured screens</div><div class="test-status">failed</div><div class="test-duration">0.077s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 200
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:206:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Screen Permission Configuration</div><div class="test-title">should filter screens by role</div><div class="test-status">failed</div><div class="test-duration">0.041s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 200
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:219:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Widget Permission Configuration</div><div class="test-title">should configure chart widget with permissions</div><div class="test-status">failed</div><div class="test-duration">0.074s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:241:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Widget Permission Configuration</div><div class="test-title">should configure table widget with role restrictions</div><div class="test-status">failed</div><div class="test-duration">0.027s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:261:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Widget Permission Configuration</div><div class="test-title">should configure stat card widget</div><div class="test-status">failed</div><div class="test-duration">0.092s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:278:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Widget Permission Configuration</div><div class="test-title">should reject widget with invalid configuration</div><div class="test-status">failed</div><div class="test-duration">0.069s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 400
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:300:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Widget Permission Configuration</div><div class="test-title">should update widget permissions</div><div class="test-status">failed</div><div class="test-duration">0.055s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:314:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Widget Permission Configuration</div><div class="test-title">should get widgets by screen</div><div class="test-status">failed</div><div class="test-duration">0.077s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 200
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:344:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Permission Management</div><div class="test-title">should create custom permission</div><div class="test-status">failed</div><div class="test-duration">0.222s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:366:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Permission Management</div><div class="test-title">should get all permissions</div><div class="test-status">failed</div><div class="test-duration">0.027s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:384:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Permission Management</div><div class="test-title">should get permissions by category</div><div class="test-status">failed</div><div class="test-duration">0.018s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:403:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Permission Management</div><div class="test-title">should validate permission references in configurations</div><div class="test-status">failed</div><div class="test-duration">0.103s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 400
Received: 404
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:422:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce screen configuration permissions</div><div class="test-status">failed</div><div class="test-duration">0.163s</div></div><div class="failureMessages"> <pre class="failureMsg">AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:431:27)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce widget configuration permissions</div><div class="test-status">failed</div><div class="test-duration">0.047s</div></div><div class="failureMessages"> <pre class="failureMsg">AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:447:27)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce permission creation permissions</div><div class="test-status">failed</div><div class="test-duration">0.027s</div></div><div class="failureMessages"> <pre class="failureMsg">AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:462:27)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Configuration Validation</div><div class="test-title">should validate screen accessibility for user roles</div><div class="test-status">failed</div><div class="test-duration">0.445s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:496:46)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Configuration Validation</div><div class="test-title">should validate widget visibility for user permissions</div><div class="test-status">failed</div><div class="test-duration">0.472s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:533:46)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Configuration Deletion</div><div class="test-title">should delete screen configuration</div><div class="test-status">failed</div><div class="test-duration">0.044s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:562:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Permission Configuration API Tests &gt; Configuration Deletion</div><div class="test-title">should delete widget configuration</div><div class="test-status">failed</div><div class="test-duration">0.062s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\permission_config.test.js:593:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div></div></div><div id="suite-3" class="suite-container"><input id="collapsible-2" type="checkbox" class="toggle" checked="checked"/><label for="collapsible-2"><div class="suite-info"><div class="suite-path">D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js</div><div class="suite-time">1.846s</div></div></label><div class="suite-tests"><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should create custom role with valid data</div><div class="test-status">failed</div><div class="test-duration">0.001s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createSiteCoordinator is not a function
    at Object.createSiteCoordinator (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:43:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should create facilities manager role</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createFacilitiesManager is not a function
    at Object.createFacilitiesManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:63:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should create security supervisor role</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createSecuritySupervisor is not a function
    at Object.createSecuritySupervisor (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:81:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should create escalation manager role</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createEscalationRole is not a function
    at Object.createEscalationRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:99:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should reject role with invalid name</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createInvalid is not a function
    at Object.createInvalid (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:116:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result passed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should reject role with missing required fields</div><div class="test-status">passed</div><div class="test-duration">0.146s</div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should reject duplicate role name</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:150:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should create role with minimal permissions</div><div class="test-status">failed</div><div class="test-duration">0.001s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createMinimalRole is not a function
    at Object.createMinimalRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:176:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should create role with maximum permissions</div><div class="test-status">failed</div><div class="test-duration">0.001s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createMaximalRole is not a function
    at Object.createMaximalRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:193:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Retrieval</div><div class="test-title">should get all roles</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:215:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Retrieval</div><div class="test-title">should get role by ID</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:215:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Retrieval</div><div class="test-title">should return 404 for non-existent role</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:215:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Retrieval</div><div class="test-title">should filter roles by type</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:215:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Retrieval</div><div class="test-title">should search roles by name</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:215:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Updates</div><div class="test-title">should update role details</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:306:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Updates</div><div class="test-title">should add permissions to role</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:306:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Updates</div><div class="test-title">should remove permissions from role</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:306:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Updates</div><div class="test-title">should reject update with invalid data</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:306:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Updates</div><div class="test-title">should prevent updating system roles</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:306:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Permission Management</div><div class="test-title">should get all available permissions</div><div class="test-status">failed</div><div class="test-duration">0.02s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:432:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Permission Management</div><div class="test-title">should get permissions by category</div><div class="test-status">failed</div><div class="test-duration">0.019s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:451:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Permission Management</div><div class="test-title">should validate permission assignments</div><div class="test-status">failed</div><div class="test-duration">0.001s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:460:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce role creation permissions</div><div class="test-status">failed</div><div class="test-duration">0.073s</div></div><div class="failureMessages"> <pre class="failureMsg">AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:479:27)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce role update permissions</div><div class="test-status">failed</div><div class="test-duration">0.024s</div></div><div class="failureMessages"> <pre class="failureMsg">AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:495:27)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce role deletion permissions</div><div class="test-status">failed</div><div class="test-duration">0.027s</div></div><div class="failureMessages"> <pre class="failureMsg">AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:510:27)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Deletion</div><div class="test-title">should delete custom role</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: RoleFactory.createCustomRole is not a function
    at Object.createCustomRole (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:526:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Deletion</div><div class="test-title">should prevent deletion of system roles</div><div class="test-status">failed</div><div class="test-duration">0.019s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: rolesResponse.data.data.find is not a function
    at Object.find (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:563:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Deletion</div><div class="test-title">should prevent deletion of role assigned to users</div><div class="test-status">failed</div><div class="test-duration">0.019s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: rolesResponse.data.data.find is not a function
    at Object.find (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:586:59)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result passed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Deletion</div><div class="test-title">should return 404 when deleting non-existent role</div><div class="test-status">passed</div><div class="test-duration">0.76s</div></div></div></div></div></div></body></html>