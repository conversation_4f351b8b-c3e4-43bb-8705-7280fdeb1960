#!/usr/bin/env node

/**
 * Merge Swagger Documentation Script
 *
 * This script merges all modular swagger documentation files from the
 * swagger-api-docs folder into a single comprehensive swagger.json file.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const SWAGGER_DOCS_DIR = path.join(__dirname, '../swagger-api-docs');
const OUTPUT_FILE = path.join(__dirname, '../swagger.json');

// Files to merge (in order)
const DOCUMENTATION_FILES = [
  'main.json',           // Base configuration
  'schemas.json',        // Data models
  'auth.json',           // Authentication endpoints
  'properties.json',     // Property management
  'maintenance.json',    // Maintenance management
  'dashboard.json',      // Dashboard and analytics
  'notifications.json',  // Notification management
  'missing-endpoints.json', // Missing/planned endpoints
  // Add more files as they are created
];

/**
 * Load and parse JSON file
 */
function loadJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ Error loading ${filePath}:`, error.message);
    return null;
  }
}

/**
 * Deep merge objects
 */
function deepMerge(target, source) {
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      if (!target[key]) target[key] = {};
      deepMerge(target[key], source[key]);
    } else {
      target[key] = source[key];
    }
  }
  return target;
}

/**
 * Merge all swagger documentation files
 */
function mergeSwaggerDocs() {
  console.log('🔄 Starting swagger documentation merge...\n');

  // Start with the main configuration
  const mainFile = path.join(SWAGGER_DOCS_DIR, 'main.json');
  const mergedDoc = loadJsonFile(mainFile);

  if (!mergedDoc) {
    console.error('❌ Failed to load main.json. Aborting merge.');
    process.exit(1);
  }

  console.log('✅ Loaded main.json');

  // Initialize paths and components if they don't exist
  if (!mergedDoc.paths) mergedDoc.paths = {};
  if (!mergedDoc.components) mergedDoc.components = {};
  if (!mergedDoc.components.schemas) mergedDoc.components.schemas = {};

  // Merge each documentation file
  for (const fileName of DOCUMENTATION_FILES) {
    if (fileName === 'main.json') continue; // Already loaded

    const filePath = path.join(SWAGGER_DOCS_DIR, fileName);

    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  Skipping ${fileName} (file not found)`);
      continue;
    }

    const doc = loadJsonFile(filePath);
    if (!doc) {
      console.log(`⚠️  Skipping ${fileName} (failed to parse)`);
      continue;
    }

    // Merge paths
    if (doc.paths) {
      Object.assign(mergedDoc.paths, doc.paths);
      console.log(`✅ Merged paths from ${fileName}`);
    }

    // Merge schemas
    if (doc.components && doc.components.schemas) {
      Object.assign(mergedDoc.components.schemas, doc.components.schemas);
      console.log(`✅ Merged schemas from ${fileName}`);
    }

    // Merge other components (responses, parameters, etc.)
    if (doc.components) {
      const { schemas, ...otherComponents } = doc.components;
      deepMerge(mergedDoc.components, otherComponents);
    }
  }

  // Add metadata about the merge
  mergedDoc.info.description += `\n\nGenerated by merging modular documentation files on ${new Date().toISOString()}`;

  // Count endpoints and schemas
  const endpointCount = Object.keys(mergedDoc.paths).length;
  const schemaCount = Object.keys(mergedDoc.components.schemas || {}).length;

  console.log(`\n📊 Merge Summary:`);
  console.log(`   📍 Endpoints: ${endpointCount}`);
  console.log(`   📋 Schemas: ${schemaCount}`);

  return mergedDoc;
}

/**
 * Validate the merged documentation
 */
function validateMergedDoc(doc) {
  console.log('\n🔍 Validating merged documentation...');

  const errors = [];

  // Check required fields
  if (!doc.openapi) errors.push('Missing openapi version');
  if (!doc.info) errors.push('Missing info section');
  if (!doc.paths) errors.push('Missing paths section');

  // Check for duplicate paths
  const paths = Object.keys(doc.paths);
  const duplicates = paths.filter((path, index) => paths.indexOf(path) !== index);
  if (duplicates.length > 0) {
    errors.push(`Duplicate paths found: ${duplicates.join(', ')}`);
  }

  // Check for missing schema references
  const pathsStr = JSON.stringify(doc.paths);
  const schemaRefs = pathsStr.match(/"\$ref":\s*"#\/components\/schemas\/(\w+)"/g) || [];
  const availableSchemas = Object.keys(doc.components.schemas || {});

  for (const ref of schemaRefs) {
    const schemaName = ref.match(/schemas\/(\w+)"/)[1];
    if (!availableSchemas.includes(schemaName)) {
      errors.push(`Missing schema reference: ${schemaName}`);
    }
  }

  if (errors.length > 0) {
    console.log('❌ Validation errors found:');
    errors.forEach(error => console.log(`   - ${error}`));
    return false;
  }

  console.log('✅ Validation passed');
  return true;
}

/**
 * Write the merged documentation to file
 */
function writeOutput(doc) {
  try {
    const jsonString = JSON.stringify(doc, null, 2);
    fs.writeFileSync(OUTPUT_FILE, jsonString, 'utf8');

    const fileSize = (fs.statSync(OUTPUT_FILE).size / 1024).toFixed(2);
    console.log(`\n✅ Merged documentation written to: ${OUTPUT_FILE}`);
    console.log(`📁 File size: ${fileSize} KB`);

    return true;
  } catch (error) {
    console.error(`❌ Error writing output file:`, error.message);
    return false;
  }
}

/**
 * Main execution
 */
function main() {
  console.log('🚀 SRSR Property Management API Documentation Merger\n');

  // Check if swagger-api-docs directory exists
  if (!fs.existsSync(SWAGGER_DOCS_DIR)) {
    console.error(`❌ Swagger docs directory not found: ${SWAGGER_DOCS_DIR}`);
    process.exit(1);
  }

  try {
    // Merge documentation
    const mergedDoc = mergeSwaggerDocs();

    // Validate merged documentation
    if (!validateMergedDoc(mergedDoc)) {
      console.error('\n❌ Validation failed. Please fix errors before proceeding.');
      process.exit(1);
    }

    // Write output
    if (!writeOutput(mergedDoc)) {
      console.error('\n❌ Failed to write output file.');
      process.exit(1);
    }

    console.log('\n🎉 Swagger documentation merge completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Review the generated swagger.json file');
    console.log('   2. Test with swagger validator: swagger-codegen validate -i swagger.json');
    console.log('   3. Serve with Swagger UI: npx swagger-ui-serve swagger.json');
    console.log('   4. Update /api-docs endpoint to serve the new documentation');

  } catch (error) {
    console.error('\n💥 Unexpected error during merge:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  mergeSwaggerDocs,
  validateMergedDoc,
  writeOutput
};
