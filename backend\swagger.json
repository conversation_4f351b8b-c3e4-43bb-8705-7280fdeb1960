{"openapi": "3.0.3", "info": {"title": "SRSR Property Management API", "description": "Comprehensive backend API server for SRSR Property Management System with NextJS, PostgreSQL, and role-based access control\n\nGenerated by merging modular documentation files on 2025-06-01T08:48:17.638Z", "version": "1.0.0", "contact": {"name": "SRSR Property Management", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.srsr.com", "description": "Production server"}, {"url": "https://staging-api.srsr.com", "description": "Staging server"}], "security": [{"bearerAuth": []}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token obtained from /api/auth/login endpoint"}}, "parameters": {"PageParam": {"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, "LimitParam": {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, "SearchParam": {"name": "search", "in": "query", "description": "Search term for filtering", "required": false, "schema": {"type": "string", "minLength": 1}}}, "responses": {"UnauthorizedError": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": false, "error": "Authentication required", "code": "UNAUTHORIZED"}}}}, "ForbiddenError": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": false, "error": "Insufficient permissions", "code": "FORBIDDEN"}}}}, "NotFoundError": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": false, "error": "Resource not found", "code": "NOT_FOUND"}}}}, "ValidationError": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": false, "error": "Validation failed", "code": "VALIDATION_ERROR"}}}}, "InternalServerError": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": false, "error": "Internal server error", "code": "INTERNAL_ERROR"}}}}}, "schemas": {"ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates if the request was successful"}, "data": {"description": "Response data (varies by endpoint)"}, "error": {"type": "string", "description": "Error message if success is false"}, "code": {"type": "string", "description": "Error code for programmatic handling"}}, "required": ["success"], "example": {"success": true, "data": {}}}, "PaginationResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Items per page"}, "total": {"type": "integer", "description": "Total number of items"}, "pages": {"type": "integer", "description": "Total number of pages"}, "has_next": {"type": "boolean", "description": "Whether there is a next page"}, "has_prev": {"type": "boolean", "description": "Whether there is a previous page"}}}}, "required": ["success", "data", "pagination"]}, "User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique user identifier"}, "email": {"type": "string", "format": "email", "description": "User email address"}, "username": {"type": "string", "nullable": true, "description": "Optional username"}, "full_name": {"type": "string", "description": "User's full name"}, "phone": {"type": "string", "nullable": true, "pattern": "^[+]?[1-9]\\d{1,14}$", "description": "User's phone number"}, "is_active": {"type": "boolean", "description": "Whether the user account is active"}, "roles": {"type": "array", "items": {"type": "string"}, "description": "List of user roles"}, "permissions": {"type": "array", "items": {"type": "string"}, "description": "List of user permissions"}, "created_at": {"type": "string", "format": "date-time", "description": "User creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}, "required": ["id", "email", "full_name", "is_active", "roles", "created_at"]}, "Property": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique property identifier"}, "name": {"type": "string", "description": "Property name"}, "type": {"type": "string", "enum": ["residential", "office", "construction_site"], "description": "Type of property"}, "parent_property_id": {"type": "string", "format": "uuid", "nullable": true, "description": "Parent property ID for hierarchical properties"}, "address": {"type": "string", "nullable": true, "description": "Property address"}, "description": {"type": "string", "nullable": true, "description": "Property description"}, "location": {"type": "string", "nullable": true, "description": "Property location details"}, "image_url": {"type": "string", "nullable": true, "description": "Property image URL"}, "capacity": {"type": "integer", "minimum": 1, "nullable": true, "description": "Office capacity (office type only)"}, "department": {"type": "string", "nullable": true, "description": "Department (office type only)"}, "project_type": {"type": "string", "nullable": true, "description": "Project type (construction site only)"}, "start_date": {"type": "string", "format": "date", "nullable": true, "description": "Start date (construction site only)"}, "expected_end_date": {"type": "string", "format": "date", "nullable": true, "description": "Expected end date (construction site only)"}, "hourly_rate_standard": {"type": "number", "minimum": 0, "nullable": true, "description": "Standard hourly rate (construction site only)"}, "is_active": {"type": "boolean", "description": "Whether the property is active"}, "services": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyService"}, "description": "Property services"}, "created_at": {"type": "string", "format": "date-time", "description": "Property creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}, "required": ["id", "name", "type", "is_active", "created_at"]}, "PropertyService": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "service_type": {"type": "string", "enum": ["electricity", "water", "internet", "security", "hvac", "plumbing", "general"]}, "status": {"type": "string", "enum": ["operational", "warning", "critical", "offline"]}, "last_checked": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "required": ["id", "service_type", "status"]}, "MaintenanceIssue": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "property_id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string", "enum": ["open", "in_progress", "resolved", "closed"]}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "service_type": {"type": "string", "enum": ["electricity", "water", "internet", "security", "hvac", "plumbing", "general"]}, "department": {"type": "string", "nullable": true}, "reported_by": {"type": "string", "format": "uuid"}, "assigned_to": {"type": "string", "format": "uuid", "nullable": true}, "due_date": {"type": "string", "format": "date", "nullable": true}, "estimated_cost": {"type": "number", "minimum": 0, "nullable": true}, "actual_cost": {"type": "number", "minimum": 0, "nullable": true}, "location_details": {"type": "string", "nullable": true}, "resolution_notes": {"type": "string", "nullable": true}, "escalation_level": {"type": "integer", "minimum": 0, "maximum": 5, "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "resolved_at": {"type": "string", "format": "date-time", "nullable": true}}, "required": ["id", "property_id", "title", "description", "status", "priority", "reported_by", "created_at"]}, "Role": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "is_system_role": {"type": "boolean", "description": "Whether this is a system-defined role"}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/Permission"}}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "is_system_role", "created_at"]}, "Permission": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "resource": {"type": "string", "description": "Resource this permission applies to"}, "action": {"type": "string", "description": "Action this permission allows"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "resource", "action", "created_at"]}, "Notification": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "message": {"type": "string"}, "type": {"type": "string", "enum": ["maintenance", "threshold", "system", "user"]}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "is_read": {"type": "boolean"}, "recipient_id": {"type": "string", "format": "uuid"}, "related_entity_id": {"type": "string", "format": "uuid", "nullable": true}, "related_entity_type": {"type": "string", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "read_at": {"type": "string", "format": "date-time", "nullable": true}}, "required": ["id", "title", "message", "type", "priority", "is_read", "recipient_id", "created_at"]}, "LoginRequest": {"type": "object", "properties": {"identifier": {"type": "string", "description": "Email, username, or phone number"}, "password": {"type": "string", "minLength": 6, "description": "User password"}, "login_type": {"type": "string", "enum": ["email", "username", "phone"], "description": "Optional login type hint for faster processing"}}, "required": ["identifier", "password"]}, "LegacyLoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "User email address"}, "password": {"type": "string", "minLength": 6, "description": "User password"}}, "required": ["email", "password"]}, "LoginResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "token": {"type": "string", "description": "JWT authentication token"}, "user": {"$ref": "#/components/schemas/User"}}, "required": ["success", "token", "user"]}, "RegisterRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "User email address"}, "username": {"type": "string", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z0-9_-]+$", "description": "Unique username"}, "password": {"type": "string", "minLength": 6, "description": "User password"}, "full_name": {"type": "string", "minLength": 2, "description": "User's full name"}, "phone": {"type": "string", "pattern": "^[+]?[1-9]\\d{1,14}$", "description": "User's phone number"}}, "required": ["email", "password", "full_name"]}, "RegisterResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"message": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}}}, "required": ["success", "data"]}, "CreatePropertyRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2, "description": "Property name"}, "type": {"type": "string", "enum": ["residential", "office", "construction_site"], "description": "Type of property"}, "parent_property_id": {"type": "string", "format": "uuid", "description": "Parent property ID for hierarchical properties"}, "address": {"type": "string", "description": "Property address"}, "description": {"type": "string", "description": "Property description"}, "location": {"type": "string", "description": "Property location details"}, "capacity": {"type": "integer", "minimum": 1, "description": "Required for office type"}, "department": {"type": "string", "description": "Optional for office type"}, "project_type": {"type": "string", "description": "Required for construction_site type"}, "start_date": {"type": "string", "format": "date", "description": "Required for construction_site type"}, "expected_end_date": {"type": "string", "format": "date", "description": "Required for construction_site type"}, "hourly_rate_standard": {"type": "number", "minimum": 0, "description": "Optional for construction_site type"}}, "required": ["name", "type"]}, "UpdatePropertyRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2}, "address": {"type": "string"}, "description": {"type": "string"}, "location": {"type": "string"}, "is_active": {"type": "boolean"}, "capacity": {"type": "integer", "minimum": 1}, "department": {"type": "string"}, "project_type": {"type": "string"}, "start_date": {"type": "string", "format": "date"}, "expected_end_date": {"type": "string", "format": "date"}, "hourly_rate_standard": {"type": "number", "minimum": 0}}}, "PropertyMember": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "user_id": {"type": "string", "format": "uuid"}, "property_id": {"type": "string", "format": "uuid"}, "role": {"type": "string"}, "position": {"type": "string"}, "department": {"type": "string"}, "hourly_rate": {"type": "number", "minimum": 0}, "start_date": {"type": "string", "format": "date"}, "end_date": {"type": "string", "format": "date", "nullable": true}, "user": {"$ref": "#/components/schemas/User"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["id", "user_id", "property_id", "role", "created_at"]}, "AddPropertyMemberRequest": {"type": "object", "properties": {"user_id": {"type": "string", "format": "uuid"}, "role": {"type": "string", "description": "Role at the property"}, "position": {"type": "string", "description": "Job position/title"}, "department": {"type": "string", "description": "Department or team"}, "hourly_rate": {"type": "number", "minimum": 0, "description": "Hourly rate for the member"}, "start_date": {"type": "string", "format": "date", "description": "Start date at the property"}, "end_date": {"type": "string", "format": "date", "description": "End date at the property (optional)"}}, "required": ["user_id", "role"]}, "CreateMaintenanceIssueRequest": {"type": "object", "properties": {"property_id": {"type": "string", "format": "uuid"}, "title": {"type": "string", "minLength": 3}, "description": {"type": "string", "minLength": 10}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"], "default": "medium"}, "service_type": {"type": "string", "enum": ["electricity", "water", "internet", "security", "hvac", "plumbing", "general"]}, "department": {"type": "string"}, "due_date": {"type": "string", "format": "date"}, "estimated_cost": {"type": "number", "minimum": 0}, "location_details": {"type": "string"}}, "required": ["property_id", "title", "description"]}, "UpdateMaintenanceIssueRequest": {"type": "object", "properties": {"title": {"type": "string", "minLength": 3}, "description": {"type": "string", "minLength": 10}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "status": {"type": "string", "enum": ["open", "in_progress", "resolved", "closed"]}, "service_type": {"type": "string", "enum": ["electricity", "water", "internet", "security", "hvac", "plumbing", "general"]}, "department": {"type": "string"}, "due_date": {"type": "string", "format": "date"}, "estimated_cost": {"type": "number", "minimum": 0}, "actual_cost": {"type": "number", "minimum": 0}, "location_details": {"type": "string"}, "resolution_notes": {"type": "string"}}}, "UpdateMaintenanceStatusRequest": {"type": "object", "properties": {"status": {"type": "string", "enum": ["open", "in_progress", "resolved", "closed"]}, "resolution_notes": {"type": "string"}}, "required": ["status"]}, "AssignMaintenanceIssueRequest": {"type": "object", "properties": {"assigned_to": {"type": "string", "format": "uuid", "description": "User ID to assign the issue to"}, "assignment_notes": {"type": "string", "description": "Notes about the assignment"}, "due_date": {"type": "string", "format": "date", "description": "Due date for completion"}}, "required": ["assigned_to"]}, "EscalateMaintenanceIssueRequest": {"type": "object", "properties": {"escalation_reason": {"type": "string", "minLength": 5, "description": "Reason for escalation"}, "escalation_level": {"type": "integer", "minimum": 1, "maximum": 5, "description": "Escalation level (1-5)"}}}, "DashboardStatus": {"type": "object", "properties": {"properties": {"type": "object", "properties": {"total": {"type": "integer", "description": "Total number of properties"}, "operational": {"type": "integer", "description": "Number of operational properties"}, "warning": {"type": "integer", "description": "Number of properties with warnings"}, "critical": {"type": "integer", "description": "Number of properties with critical issues"}}, "required": ["total", "operational", "warning", "critical"]}, "maintenance_issues": {"type": "object", "properties": {"total": {"type": "integer", "description": "Total number of maintenance issues"}, "open": {"type": "integer", "description": "Number of open issues"}, "in_progress": {"type": "integer", "description": "Number of issues in progress"}, "critical": {"type": "integer", "description": "Number of critical issues"}}, "required": ["total", "open", "in_progress", "critical"]}, "recent_alerts": {"type": "array", "items": {"$ref": "#/components/schemas/DashboardAlert"}, "description": "Recent system alerts"}, "system_health": {"type": "object", "properties": {"api_status": {"type": "string", "enum": ["operational", "degraded", "down"], "description": "API service status"}, "database_status": {"type": "string", "enum": ["operational", "degraded", "down"], "description": "Database service status"}, "last_backup": {"type": "string", "format": "date-time", "description": "Last successful backup timestamp"}, "uptime_percentage": {"type": "number", "minimum": 0, "maximum": 100, "description": "System uptime percentage"}}, "required": ["api_status", "database_status", "uptime_percentage"]}, "quick_stats": {"type": "object", "properties": {"active_users": {"type": "integer", "description": "Number of currently active users"}, "total_notifications": {"type": "integer", "description": "Total unread notifications"}, "pending_approvals": {"type": "integer", "description": "Number of items pending approval"}}, "required": ["active_users", "total_notifications", "pending_approvals"]}}, "required": ["properties", "maintenance_issues", "recent_alerts", "system_health", "quick_stats"]}, "DashboardAlert": {"type": "object", "properties": {"type": {"type": "string", "enum": ["maintenance", "threshold", "system", "user"], "description": "Type of alert"}, "message": {"type": "string", "description": "Alert message"}, "timestamp": {"type": "string", "format": "date-time", "description": "Alert timestamp"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"], "description": "Alert severity level"}, "property_id": {"type": "string", "format": "uuid", "nullable": true, "description": "Related property ID if applicable"}, "related_entity_id": {"type": "string", "format": "uuid", "nullable": true, "description": "Related entity ID if applicable"}}, "required": ["type", "message", "timestamp", "severity"]}, "CreateNotificationRequest": {"type": "object", "properties": {"title": {"type": "string", "minLength": 3, "maxLength": 200, "description": "Notification title"}, "message": {"type": "string", "minLength": 5, "maxLength": 1000, "description": "Notification message"}, "type": {"type": "string", "enum": ["maintenance", "threshold", "system", "user"], "description": "Type of notification"}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"], "default": "medium", "description": "Notification priority level"}, "recipient_ids": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "Array of user IDs to send notification to"}, "send_to_all": {"type": "boolean", "description": "Send to all users", "default": false}, "related_entity_id": {"type": "string", "format": "uuid", "description": "ID of related entity (maintenance issue, property, etc.)"}, "related_entity_type": {"type": "string", "description": "Type of related entity"}, "scheduled_for": {"type": "string", "format": "date-time", "description": "Schedule notification for future delivery"}, "expires_at": {"type": "string", "format": "date-time", "description": "Notification expiration time"}}, "required": ["title", "message", "type"], "oneOf": [{"properties": {"send_to_all": {"const": true}}}, {"properties": {"recipient_ids": {"minItems": 1}}, "required": ["recipient_ids"]}]}}}, "paths": {"/api": {"get": {"summary": "Get API information", "description": "Returns API information, available endpoints, and system status", "tags": ["System"], "security": [], "responses": {"200": {"description": "API information retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": true, "data": {"name": "SRSR Property Management API", "version": "1.0.0", "status": "operational", "timestamp": "2024-01-15T10:30:00Z"}}}}}}}}, "/api/auth/login": {"post": {"summary": "User login", "description": "Authenticate user with email/username/phone and password. Supports multi-field login for flexible authentication.", "tags": ["Authentication"], "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/LoginRequest"}, {"$ref": "#/components/schemas/LegacyLoginRequest"}]}, "examples": {"multi_field_login": {"summary": "Multi-field login (recommended)", "value": {"identifier": "<EMAIL>", "password": "admin123", "login_type": "email"}}, "username_login": {"summary": "<PERSON>rname login", "value": {"identifier": "admin_user", "password": "admin123", "login_type": "username"}}, "phone_login": {"summary": "Phone login", "value": {"identifier": "+**********", "password": "admin123", "login_type": "phone"}}, "legacy_login": {"summary": "Legacy email login (backward compatibility)", "value": {"email": "<EMAIL>", "password": "admin123"}}}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}, "example": {"success": true, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "user": {"id": "123e4567-e89b-12d3-a456-************", "email": "<EMAIL>", "full_name": "System Administrator", "roles": ["admin"], "permissions": ["*"]}}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"description": "Invalid credentials or account deactivated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "examples": {"invalid_credentials": {"summary": "Invalid credentials", "value": {"success": false, "error": "Invalid credentials", "code": "INVALID_CREDENTIALS"}}, "account_deactivated": {"summary": "Account deactivated", "value": {"success": false, "error": "Account is deactivated", "code": "ACCOUNT_DEACTIVATED"}}}}}}}}}, "/api/auth/register": {"post": {"summary": "Register new user", "description": "Register a new user account. Only admin users can register new users.", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}, "example": {"email": "<EMAIL>", "username": "newuser", "password": "securepassword123", "full_name": "New User", "phone": "+**********"}}}}, "responses": {"201": {"description": "User registered successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterResponse"}, "example": {"success": true, "data": {"message": "User registered successfully", "user": {"id": "123e4567-e89b-12d3-a456-************", "email": "<EMAIL>", "full_name": "New User", "is_active": true, "roles": []}}}}}}, "400": {"description": "Validation error or user already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "examples": {"validation_error": {"summary": "Validation error", "value": {"success": false, "error": "Validation failed", "code": "VALIDATION_ERROR"}}, "user_exists": {"summary": "User already exists", "value": {"success": false, "error": "User with this email already exists", "code": "USER_EXISTS"}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "403": {"$ref": "#/components/responses/ForbiddenError"}}}}, "/api/auth/me": {"get": {"summary": "Get current user profile", "description": "Get the profile information of the currently authenticated user", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "User profile retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/User"}}}]}, "example": {"success": true, "data": {"id": "123e4567-e89b-12d3-a456-************", "email": "<EMAIL>", "username": "admin", "full_name": "System Administrator", "phone": "+**********", "is_active": true, "roles": ["admin"], "permissions": ["*"], "created_at": "2024-01-01T00:00:00Z"}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/api/properties": {"get": {"summary": "Get all properties", "description": "Retrieve a paginated list of properties with optional filtering by type", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/LimitParam"}, {"name": "type", "in": "query", "description": "Filter properties by type", "required": false, "schema": {"type": "string", "enum": ["residential", "office", "construction_site"]}}, {"name": "status", "in": "query", "description": "Filter properties by status", "required": false, "schema": {"type": "string", "enum": ["active", "inactive"]}}, {"$ref": "#/components/parameters/SearchParam"}], "responses": {"200": {"description": "Properties retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Property"}}}}]}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "post": {"summary": "Create new property", "description": "Create a new property (residential, office, or construction site). Requires admin or property_manager role.", "tags": ["Properties"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePropertyRequest"}, "examples": {"residential": {"summary": "Residential property", "value": {"name": "Sunset Apartments", "type": "residential", "address": "123 Main Street, City, State", "description": "Modern residential complex"}}, "office": {"summary": "Office property", "value": {"name": "Corporate Headquarters", "type": "office", "address": "456 Business Ave, City, State", "capacity": 100, "department": "IT"}}, "construction_site": {"summary": "Construction site", "value": {"name": "New Mall Construction", "type": "construction_site", "address": "789 Development Rd, City, State", "project_type": "Commercial Building", "start_date": "2024-01-01", "expected_end_date": "2024-12-31", "hourly_rate_standard": 25.5}}}}}}, "responses": {"201": {"description": "Property created successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Property"}}}]}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "403": {"$ref": "#/components/responses/ForbiddenError"}}}}, "/api/properties/{id}": {"get": {"summary": "Get property by ID", "description": "Retrieve a specific property by its ID with all related information", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Property retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Property"}}}]}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}, "put": {"summary": "Update property", "description": "Update a property's information. Requires admin or property_manager role.", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePropertyRequest"}}}}, "responses": {"200": {"description": "Property updated successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Property"}}}]}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "403": {"$ref": "#/components/responses/ForbiddenError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/api/properties/{id}/members": {"get": {"summary": "Get property members", "description": "Retrieve all members assigned to a property", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Property members retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyMember"}}}}]}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}, "post": {"summary": "Add property member", "description": "Add a new member to a property", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddPropertyMemberRequest"}}}}, "responses": {"201": {"description": "Property member added successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/api/maintenance": {"get": {"summary": "Get maintenance issues", "description": "Retrieve a paginated list of maintenance issues with filtering options", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/LimitParam"}, {"name": "property_id", "in": "query", "description": "Filter by property ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "status", "in": "query", "description": "Filter by status", "required": false, "schema": {"type": "string", "enum": ["open", "in_progress", "resolved", "closed"]}}, {"name": "priority", "in": "query", "description": "Filter by priority", "required": false, "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "assigned_to", "in": "query", "description": "Filter by assigned user ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "service_type", "in": "query", "description": "Filter by service type", "required": false, "schema": {"type": "string", "enum": ["electricity", "water", "internet", "security", "hvac", "plumbing", "general"]}}], "responses": {"200": {"description": "Maintenance issues retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "post": {"summary": "Create maintenance issue", "description": "Create a new maintenance issue", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMaintenanceIssueRequest"}, "examples": {"electrical_issue": {"summary": "Electrical issue", "value": {"property_id": "123e4567-e89b-12d3-a456-************", "title": "Power outage in Building A", "description": "Complete power outage affecting all units in Building A. Emergency repair needed.", "priority": "critical", "service_type": "electricity", "department": "Maintenance", "due_date": "2024-01-16", "estimated_cost": 500, "location_details": "Building A, Main electrical panel"}}, "plumbing_issue": {"summary": "Plumbing issue", "value": {"property_id": "123e4567-e89b-12d3-a456-************", "title": "Leaky faucet in Unit 205", "description": "Kitchen faucet is dripping continuously, causing water waste.", "priority": "medium", "service_type": "plumbing", "estimated_cost": 75, "location_details": "Unit 205, Kitchen"}}}}}}, "responses": {"201": {"description": "Maintenance issue created successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/MaintenanceIssue"}}}]}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/maintenance/{id}": {"get": {"summary": "Get maintenance issue by ID", "description": "Retrieve a specific maintenance issue with full details", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Maintenance issue retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/MaintenanceIssue"}}}]}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}, "put": {"summary": "Update maintenance issue", "description": "Update a maintenance issue's information", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMaintenanceIssueRequest"}}}}, "responses": {"200": {"description": "Maintenance issue updated successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/MaintenanceIssue"}}}]}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}, "patch": {"summary": "Update maintenance issue status", "description": "Update only the status of a maintenance issue", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMaintenanceStatusRequest"}}}}, "responses": {"200": {"description": "Maintenance issue status updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}, "delete": {"summary": "Delete maintenance issue", "description": "Delete a maintenance issue", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Maintenance issue deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/api/maintenance/{id}/assign": {"post": {"summary": "Assign maintenance issue", "description": "Assign a maintenance issue to a user", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignMaintenanceIssueRequest"}}}}, "responses": {"200": {"description": "Maintenance issue assigned successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/api/maintenance/{id}/escalate": {"post": {"summary": "Escalate maintenance issue", "description": "Escalate a maintenance issue to higher priority or management", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EscalateMaintenanceIssueRequest"}}}}, "responses": {"200": {"description": "Maintenance issue escalated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/api/dashboard/status": {"get": {"summary": "Get dashboard status", "description": "Retrieve dashboard status and metrics for the system overview", "tags": ["Dashboard"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Dashboard status retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/DashboardStatus"}}}]}, "example": {"success": true, "data": {"properties": {"total": 15, "operational": 12, "warning": 2, "critical": 1}, "maintenance_issues": {"total": 8, "open": 3, "in_progress": 4, "critical": 1}, "recent_alerts": [{"type": "threshold", "message": "Generator fuel level below threshold at Property A", "timestamp": "2024-01-15T10:30:00Z", "severity": "warning"}, {"type": "maintenance", "message": "Critical electrical issue reported at Building B", "timestamp": "2024-01-15T09:15:00Z", "severity": "critical"}], "system_health": {"api_status": "operational", "database_status": "operational", "last_backup": "2024-01-15T02:00:00Z", "uptime_percentage": 99.8}, "quick_stats": {"active_users": 45, "total_notifications": 12, "pending_approvals": 3}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/api/notifications": {"get": {"summary": "Get notifications", "description": "Retrieve notifications for the current user with pagination and filtering", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/LimitParam"}, {"name": "is_read", "in": "query", "description": "Filter by read status", "required": false, "schema": {"type": "boolean"}}, {"name": "type", "in": "query", "description": "Filter by notification type", "required": false, "schema": {"type": "string", "enum": ["maintenance", "threshold", "system", "user"]}}, {"name": "priority", "in": "query", "description": "Filter by priority level", "required": false, "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "unread_only", "in": "query", "description": "Show only unread notifications", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Notifications retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse"}, "example": {"success": true, "data": [{"id": "123e4567-e89b-12d3-a456-************", "title": "Maintenance Issue Assigned", "message": "You have been assigned a new maintenance issue: Power outage in Building A", "type": "maintenance", "priority": "critical", "is_read": false, "related_entity_id": "456e7890-e89b-12d3-a456-************", "related_entity_type": "maintenance_issue", "created_at": "2024-01-15T10:30:00Z"}], "pagination": {"page": 1, "limit": 10, "total": 25, "pages": 3, "has_next": true, "has_prev": false}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "post": {"summary": "Create notification", "description": "Create a new notification. Only admin users can create notifications.", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNotificationRequest"}, "examples": {"system_notification": {"summary": "System notification to all users", "value": {"title": "System Maintenance Scheduled", "message": "System maintenance is scheduled for tonight from 2:00 AM to 4:00 AM. Please save your work.", "type": "system", "priority": "medium", "send_to_all": true}}, "targeted_notification": {"summary": "Notification to specific users", "value": {"title": "New Property Assignment", "message": "You have been assigned to manage the new Sunset Apartments property.", "type": "user", "priority": "medium", "recipient_ids": ["123e4567-e89b-12d3-a456-************"], "related_entity_id": "456e7890-e89b-12d3-a456-************", "related_entity_type": "property"}}}}}}, "responses": {"201": {"description": "Notification created successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"message": {"type": "string"}, "notification_count": {"type": "integer", "description": "Number of notifications created"}}}}}]}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "403": {"$ref": "#/components/responses/ForbiddenError"}}}}, "/api/notifications/{id}/read": {"patch": {"summary": "Mark notification as read", "description": "Mark a specific notification as read", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Notification ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Notification marked as read successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": true, "data": {"message": "Notification marked as read", "read_at": "2024-01-15T11:00:00Z"}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/api/notifications/mark-all-read": {"patch": {"summary": "Mark all notifications as read", "description": "Mark all notifications for the current user as read", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "All notifications marked as read successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": true, "data": {"message": "All notifications marked as read", "count": 15}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/notifications/sse": {"get": {"summary": "Server-Sent Events for notifications", "description": "Real-time notification stream using Server-Sent Events", "tags": ["Notifications", "Missing"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "SSE stream established", "content": {"text/event-stream": {"schema": {"type": "string"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/monitoring": {"post": {"summary": "Submit monitoring data", "description": "Submit single metric monitoring data", "tags": ["Monitoring", "Missing"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"property_id": {"type": "string", "format": "uuid"}, "service_type": {"type": "string"}, "metric_name": {"type": "string"}, "value": {"type": "number"}, "unit": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}, "required": ["property_id", "service_type", "metric_name", "value"]}}}}, "responses": {"201": {"description": "Monitoring data submitted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/monitoring/multiple": {"post": {"summary": "Submit multiple monitoring metrics", "description": "Submit multiple monitoring metrics in a single request", "tags": ["Monitoring", "Missing"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"property_id": {"type": "string", "format": "uuid"}, "metrics": {"type": "array", "items": {"type": "object", "properties": {"service_type": {"type": "string"}, "metric_name": {"type": "string"}, "value": {"type": "number"}, "unit": {"type": "string"}}, "required": ["service_type", "metric_name", "value"]}}}, "required": ["property_id", "metrics"]}}}}, "responses": {"201": {"description": "Multiple monitoring metrics submitted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/permissions/screens": {"get": {"summary": "Get screen permissions", "description": "Get all screen permission configurations", "tags": ["Permissions", "Missing"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Screen permissions retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "post": {"summary": "Create screen permission", "description": "Create a new screen permission configuration", "tags": ["Permissions", "Missing"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"screen_name": {"type": "string"}, "required_permissions": {"type": "array", "items": {"type": "string"}}, "allowed_roles": {"type": "array", "items": {"type": "string"}}}, "required": ["screen_name"]}}}}, "responses": {"201": {"description": "Screen permission created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/permissions/widgets": {"get": {"summary": "Get widget permissions", "description": "Get all widget permission configurations", "tags": ["Permissions", "Missing"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Widget permissions retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/generator-fuel/logs/{logId}": {"get": {"summary": "Get generator fuel log by ID", "description": "Retrieve a specific generator fuel log entry", "tags": ["Generator Fuel", "Missing"], "security": [{"bearerAuth": []}], "parameters": [{"name": "logId", "in": "path", "description": "Generator fuel log ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Generator fuel log retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}, "put": {"summary": "Update generator fuel log", "description": "Update a generator fuel log entry", "tags": ["Generator Fuel", "Missing"], "security": [{"bearerAuth": []}], "parameters": [{"name": "logId", "in": "path", "description": "Generator fuel log ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"fuel_level_liters": {"type": "number", "minimum": 0}, "consumption_rate": {"type": "number", "minimum": 0}, "runtime_hours": {"type": "number", "minimum": 0}, "efficiency_percentage": {"type": "number", "minimum": 0, "maximum": 100}, "notes": {"type": "string"}}}}}}, "responses": {"200": {"description": "Generator fuel log updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}, "delete": {"summary": "Delete generator fuel log", "description": "Delete a generator fuel log entry", "tags": ["Generator Fuel", "Missing"], "security": [{"bearerAuth": []}], "parameters": [{"name": "logId", "in": "path", "description": "Generator fuel log ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Generator fuel log deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}, "/api/users/{id}/roles": {"get": {"summary": "Get user roles", "description": "Get all roles assigned to a specific user", "tags": ["Users", "Missing"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User roles retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}, "post": {"summary": "Assign role to user", "description": "Assign a role to a user", "tags": ["Users", "Missing"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"role_id": {"type": "string", "format": "uuid"}}, "required": ["role_id"]}}}}, "responses": {"201": {"description": "Role assigned to user successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "404": {"$ref": "#/components/responses/NotFoundError"}}}}}, "tags": [{"name": "System", "description": "System information and health endpoints"}, {"name": "Authentication", "description": "User authentication and authorization"}, {"name": "Users", "description": "User management operations"}, {"name": "Roles", "description": "Role management and assignment"}, {"name": "Permissions", "description": "Permission configuration and management"}, {"name": "Properties", "description": "Property management (residential, office, construction sites)"}, {"name": "Maintenance", "description": "Maintenance issue tracking and management"}, {"name": "Attendance", "description": "Property attendance tracking"}, {"name": "Generator Fuel", "description": "Generator fuel monitoring and logging"}, {"name": "Diesel Additions", "description": "Diesel fuel addition tracking"}, {"name": "OTT Services", "description": "OTT service subscription management"}, {"name": "Uptime Reports", "description": "Service uptime monitoring and reporting"}, {"name": "Function Processes", "description": "Function process management and logging"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Threshold configuration and monitoring"}, {"name": "Notifications", "description": "Notification management and delivery"}, {"name": "Monitoring", "description": "System monitoring and metrics"}, {"name": "Dashboard", "description": "Dashboard data and analytics"}, {"name": "Admin", "description": "Administrative configuration endpoints"}], "externalDocs": {"description": "Find more info about SRSR Property Management", "url": "https://github.com/srsr-property-management/api-docs"}}