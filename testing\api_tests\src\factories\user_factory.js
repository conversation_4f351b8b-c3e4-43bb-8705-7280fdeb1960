const { faker } = require('@faker-js/faker');
const { v4: uuidv4 } = require('uuid');
const config = require('../config/test.config');

class UserFactory {
  /**
   * Create admin user data
   */
  static createAdmin() {
    const timestamp = Date.now();

    return {
      full_name: `Test Admin ${faker.person.firstName()}`,
      email: `${config.testData.userPrefix}.admin.${timestamp}@srsr.com`,
      password: 'TestAdmin123!',
      phone: faker.phone.number(),
      roles: ['admin']
    };
  }

  /**
   * Create property manager user data
   */
  static createPropertyManager() {
    const timestamp = Date.now();

    return {
      full_name: `Test Property Manager ${faker.person.firstName()}`,
      email: `${config.testData.userPrefix}.manager.${timestamp}@srsr.com`,
      password: 'TestManager123!',
      username: `testmanager${timestamp}`,
      phone: faker.phone.number(),
      roles: ['property_manager']
    };
  }

  /**
   * Create maintenance staff user data
   */
  static createMaintenanceStaff() {
    const timestamp = Date.now();

    return {
      full_name: `Test Maintenance Staff ${faker.person.firstName()}`,
      email: `${config.testData.userPrefix}.maintenance.${timestamp}@srsr.com`,
      password: 'TestMaintenance123!',
      username: `testmaintenance${timestamp}`,
      phone: faker.phone.number(),
      roles: ['maintenance_staff']
    };
  }

  /**
   * Create security guard user data
   */
  static createSecurityGuard() {
    const timestamp = Date.now();

    return {
      full_name: `Test Security Guard ${faker.person.firstName()}`,
      email: `${config.testData.userPrefix}.security.${timestamp}@srsr.com`,
      password: 'TestSecurity123!',
      username: `testsecurity${timestamp}`,
      phone: faker.phone.number(),
      roles: ['security_guard']
    };
  }

  /**
   * Create viewer user data
   */
  static createViewer() {
    const timestamp = Date.now();

    return {
      full_name: `Test Viewer ${faker.person.firstName()}`,
      email: `${config.testData.userPrefix}.viewer.${timestamp}@srsr.com`,
      password: 'TestViewer123!',
      username: `testviewer${timestamp}`,
      phone: faker.phone.number(),
      roles: ['househelp']
    };
  }

  /**
   * Create basic user data
   */
  static createBasicUser() {
    const timestamp = Date.now();

    return {
      full_name: `Test User ${faker.person.firstName()}`,
      email: `${config.testData.userPrefix}.basic.${timestamp}@srsr.com`,
      password: 'TestUser123!',
      username: `testuser${timestamp}`,
      phone: faker.phone.number(),
      roles: ['househelp']
    };
  }

  /**
   * Create user with invalid email
   */
  static createWithInvalidEmail() {
    return {
      full_name: 'Invalid Email User',
      email: 'invalid-email-format',
      password: 'ValidPassword123!',
      roles: ['househelp'],
      isActive: true
    };
  }

  /**
   * Create user with weak password
   */
  static createWithWeakPassword() {
    const timestamp = Date.now();

    return {
      full_name: 'Weak Password User',
      email: `${config.testData.userPrefix}.weak.${timestamp}@srsr.com`,
      password: '123',
      roles: ['househelp'],
      isActive: true
    };
  }

  /**
   * Create user with missing required fields
   */
  static createIncomplete() {
    return {
      // Missing full_name, email, password
      roles: ['househelp'],
      isActive: true
    };
  }

  /**
   * Create user with custom roles
   */
  static createWithCustomRoles(roles) {
    const timestamp = Date.now();

    return {
      full_name: `Custom Role User ${faker.person.firstName()}`,
      email: `${config.testData.userPrefix}.custom.${timestamp}@srsr.com`,
      password: 'CustomRole123!',
      username: `customrole${timestamp}`,
      phone: faker.phone.number(),
      roles: roles,
      isActive: true
    };
  }

  /**
   * Create user for role assignment testing
   */
  static createForRoleAssignment() {
    const timestamp = Date.now();

    return {
      full_name: 'Role Assignment Test User',
      email: `${config.testData.userPrefix}.roletest.${timestamp}@srsr.com`,
      password: 'RoleTest123!',
      username: `roletest${timestamp}`,
      phone: faker.phone.number(),
      roles: ['househelp'], // Start with minimal role
      isActive: true
    };
  }

  /**
   * Create user for permission testing
   */
  static createForPermissionTesting() {
    const timestamp = Date.now();

    return {
      full_name: 'Permission Test User',
      email: `${config.testData.userPrefix}.permtest.${timestamp}@srsr.com`,
      password: 'PermTest123!',
      username: `permtest${timestamp}`,
      phone: faker.phone.number(),
      roles: ['property_manager'],
      isActive: true
    };
  }

  /**
   * Create multiple users for bulk operations
   */
  static createBulkUsers(count) {
    const users = [];

    for (let i = 0; i < count; i++) {
      const timestamp = Date.now() + i;

      users.push({
        full_name: `Bulk User ${i + 1} ${faker.person.firstName()}`,
        email: `${config.testData.userPrefix}.bulk.${timestamp}@srsr.com`,
        password: 'BulkUser123!',
        username: `bulkuser${timestamp}`,
        phone: faker.phone.number(),
        roles: ['househelp'],
        isActive: true
      });
    }

    return users;
  }

  /**
   * Create user with duplicate email
   */
  static createDuplicate(existingEmail) {
    return {
      full_name: 'Duplicate Email User',
      email: existingEmail,
      password: 'DuplicateTest123!',
      roles: ['househelp'],
      isActive: true
    };
  }

  /**
   * Create user update data
   */
  static createUpdateData() {
    return {
      full_name: `Updated User ${faker.person.firstName()}`,
      phone: faker.phone.number(),
      isActive: true
    };
  }

  /**
   * Create user with all optional fields
   */
  static createComplete() {
    const timestamp = Date.now();

    return {
      full_name: `Complete User ${faker.person.firstName()}`,
      email: `${config.testData.userPrefix}.complete.${timestamp}@srsr.com`,
      password: 'CompleteUser123!',
      username: `completeuser${timestamp}`,
      phone: faker.phone.number(),
      roles: ['property_manager'],
      isActive: true,
      address: faker.location.streetAddress(),
      city: faker.location.city(),
      state: faker.location.state(),
      zipCode: faker.location.zipCode(),
      country: faker.location.country(),
      dateOfBirth: faker.date.past({ years: 30, refDate: new Date('2000-01-01') }),
      emergencyContact: {
        name: faker.person.fullName(),
        phone: faker.phone.number(),
        relationship: 'Emergency Contact'
      }
    };
  }

  /**
   * Create user for specific test scenario
   */
  static createForScenario(scenario) {
    const timestamp = Date.now();

    const baseUser = {
      full_name: `${scenario} Test User`,
      email: `${config.testData.userPrefix}.${scenario.toLowerCase()}.${timestamp}@srsr.com`,
      password: 'ScenarioTest123!',
      username: `${scenario.toLowerCase()}${timestamp}`,
      phone: faker.phone.number(),
      isActive: true
    };

    switch (scenario.toLowerCase()) {
      case 'admin_creation':
        return { ...baseUser, roles: ['admin'] };
      case 'role_assignment':
        return { ...baseUser, roles: ['househelp'] };
      case 'permission_testing':
        return { ...baseUser, roles: ['property_manager'] };
      case 'bulk_operations':
        return { ...baseUser, roles: ['househelp'] };
      case 'validation_testing':
        return { ...baseUser, roles: ['househelp'] };
      default:
        return { ...baseUser, roles: ['househelp'] };
    }
  }

  /**
   * Generate random user data
   */
  static generateRandom() {
    const timestamp = Date.now();
    const roles = ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'househelp'];
    const randomRole = roles[Math.floor(Math.random() * roles.length)];

    return {
      full_name: faker.person.fullName(),
      email: `${config.testData.userPrefix}.random.${timestamp}@srsr.com`,
      password: faker.internet.password({ length: 12, pattern: /[A-Za-z0-9!@#$%^&*]/ }),
      username: `random${timestamp}`,
      phone: faker.phone.number(),
      roles: [randomRole],
      isActive: faker.datatype.boolean()
    };
  }
}

module.exports = UserFactory;
