import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const attendanceRecordSchema = Joi.object({
  user_id: Joi.string().uuid().required(),
  property_id: Joi.string().uuid().required(),
  date: Joi.date().required(),
  status: Joi.string().valid('present', 'absent', 'late', 'half_day', 'sick_leave', 'vacation').required(),
  check_in_time: Joi.string().optional(),
  check_out_time: Joi.string().optional(),
  hours_worked: Joi.number().min(0).max(24).optional(),
  notes: Joi.string().optional(),
});

const bulkAttendanceSchema = Joi.object({
  records: Joi.array().items(attendanceRecordSchema).min(1).required(),
});

async function bulkCreateAttendanceHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(bulkAttendanceSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400, headers: corsHeaders() }
      );
    }

    const { records } = validation.data;

    // Process attendance records in bulk
    const createdRecords = [];
    const updatedRecords = [];
    const errors = [];

    for (const recordData of records) {
      try {
        // Check if user has access to the property
        const propertyMember = await prisma.propertyMember.findFirst({
          where: {
            userId: recordData.user_id,
            propertyId: recordData.property_id,
          },
        });

        if (!propertyMember && currentUser.role !== 'admin') {
          errors.push({
            record: recordData,
            error: 'User is not assigned to this property',
          });
          continue;
        }

        // Check if attendance record already exists for this date
        const existingRecord = await prisma.attendance.findFirst({
          where: {
            userId: recordData.user_id,
            propertyId: recordData.property_id,
            date: new Date(recordData.date),
          },
        });

        const attendanceData = {
          userId: recordData.user_id,
          propertyId: recordData.property_id,
          date: new Date(recordData.date),
          status: recordData.status,
          checkInTime: recordData.check_in_time ? new Date(recordData.check_in_time) : null,
          checkOutTime: recordData.check_out_time ? new Date(recordData.check_out_time) : null,
          hoursWorked: recordData.hours_worked || 0,
          notes: recordData.notes || '',
          recordedBy: currentUser.id,
        };

        let attendanceRecord;

        if (existingRecord) {
          // Update existing record
          attendanceRecord = await prisma.attendance.update({
            where: { id: existingRecord.id },
            data: attendanceData,
            include: {
              user: {
                select: {
                  id: true,
                  fullName: true,
                  email: true,
                },
              },
              property: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          });
          updatedRecords.push(attendanceRecord);
        } else {
          // Create new record
          attendanceRecord = await prisma.attendance.create({
            data: attendanceData,
            include: {
              user: {
                select: {
                  id: true,
                  fullName: true,
                  email: true,
                },
              },
              property: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          });
          createdRecords.push(attendanceRecord);
        }
      } catch (error: any) {
        errors.push({
          record: recordData,
          error: error.message,
        });
      }
    }

    // Format response data
    const formatRecord = (record: any) => ({
      id: record.id,
      user_id: record.userId,
      property_id: record.propertyId,
      date: record.date,
      status: record.status,
      check_in_time: record.checkInTime,
      check_out_time: record.checkOutTime,
      hours_worked: record.hoursWorked,
      notes: record.notes,
      user: {
        id: record.user.id,
        full_name: record.user.fullName,
        email: record.user.email,
      },
      property: {
        id: record.property.id,
        name: record.property.name,
      },
      created_at: record.createdAt,
      updated_at: record.updatedAt,
    });

    return Response.json(
      createApiResponse({
        created: createdRecords.map(formatRecord),
        updated: updatedRecords.map(formatRecord),
        errors,
        summary: {
          total: records.length,
          created: createdRecords.length,
          updated: updatedRecords.length,
          failed: errors.length,
        },
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to bulk process attendance records');
  }
}

// POST /api/attendance/bulk - Bulk create/update attendance records
export const POST = requireAuth(bulkCreateAttendanceHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
