import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireRole } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const bulkApproveSchema = Joi.object({
  user_ids: Joi.array().items(Joi.string().uuid()).min(1).required(),
  approve: Joi.boolean().default(true),
  rejection_reason: Joi.string().when('approve', {
    is: false,
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
});

async function bulkApproveUsersHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(bulkApproveSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400, headers: corsHeaders() }
      );
    }

    const { user_ids, approve, rejection_reason } = validation.data;

    // Process users in bulk
    const processedUsers = [];
    const errors = [];

    for (const userId of user_ids) {
      try {
        // Check if user exists and is pending
        const existingUser = await prisma.user.findUnique({
          where: { id: userId },
          select: {
            id: true,
            email: true,
            fullName: true,
            isApproved: true,
            isActive: true,
          },
        });

        if (!existingUser) {
          errors.push({
            user_id: userId,
            error: 'User not found',
          });
          continue;
        }

        if (existingUser.isApproved) {
          errors.push({
            user_id: userId,
            error: 'User is already approved',
          });
          continue;
        }

        // Update user approval status
        const updatedUser = await prisma.user.update({
          where: { id: userId },
          data: {
            isApproved: approve,
            isActive: approve,
            approvedAt: approve ? new Date() : null,
            approvedBy: approve ? currentUser.id : null,
            rejectionReason: !approve ? rejection_reason : null,
            rejectedAt: !approve ? new Date() : null,
            rejectedBy: !approve ? currentUser.id : null,
          },
          select: {
            id: true,
            email: true,
            fullName: true,
            isApproved: true,
            isActive: true,
            approvedAt: true,
            rejectedAt: true,
            rejectionReason: true,
            createdAt: true,
          },
        });

        // Create notification for the user
        await prisma.notification.create({
          data: {
            userId: userId,
            title: approve ? 'Account Approved' : 'Account Rejected',
            message: approve 
              ? 'Your account has been approved. You can now access the system.'
              : `Your account has been rejected. Reason: ${rejection_reason}`,
            type: 'user',
            priority: 'medium',
            isRead: false,
          },
        });

        processedUsers.push({
          id: updatedUser.id,
          email: updatedUser.email,
          full_name: updatedUser.fullName,
          is_approved: updatedUser.isApproved,
          is_active: updatedUser.isActive,
          approved_at: updatedUser.approvedAt,
          rejected_at: updatedUser.rejectedAt,
          rejection_reason: updatedUser.rejectionReason,
          created_at: updatedUser.createdAt,
        });
      } catch (error: any) {
        errors.push({
          user_id: userId,
          error: error.message,
        });
      }
    }

    return Response.json(
      createApiResponse({
        processed_users: processedUsers,
        errors,
        summary: {
          total: user_ids.length,
          successful: processedUsers.length,
          failed: errors.length,
          action: approve ? 'approved' : 'rejected',
        },
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to bulk process user approvals');
  }
}

// POST /api/users/bulk-approve - Bulk approve/reject users
export const POST = requireRole(['admin'])(bulkApproveUsersHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
