{"openapi": "3.0.3", "info": {"title": "SRSR API - Dashboard", "version": "1.0.0"}, "paths": {"/api/dashboard/status": {"get": {"summary": "Get dashboard status", "description": "Retrieve dashboard status and metrics for the system overview", "tags": ["Dashboard"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Dashboard status retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/DashboardStatus"}}}]}, "example": {"success": true, "data": {"properties": {"total": 15, "operational": 12, "warning": 2, "critical": 1}, "maintenance_issues": {"total": 8, "open": 3, "in_progress": 4, "critical": 1}, "recent_alerts": [{"type": "threshold", "message": "Generator fuel level below threshold at Property A", "timestamp": "2024-01-15T10:30:00Z", "severity": "warning"}, {"type": "maintenance", "message": "Critical electrical issue reported at Building B", "timestamp": "2024-01-15T09:15:00Z", "severity": "critical"}], "system_health": {"api_status": "operational", "database_status": "operational", "last_backup": "2024-01-15T02:00:00Z", "uptime_percentage": 99.8}, "quick_stats": {"active_users": 45, "total_notifications": 12, "pending_approvals": 3}}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"schemas": {"DashboardStatus": {"type": "object", "properties": {"properties": {"type": "object", "properties": {"total": {"type": "integer", "description": "Total number of properties"}, "operational": {"type": "integer", "description": "Number of operational properties"}, "warning": {"type": "integer", "description": "Number of properties with warnings"}, "critical": {"type": "integer", "description": "Number of properties with critical issues"}}, "required": ["total", "operational", "warning", "critical"]}, "maintenance_issues": {"type": "object", "properties": {"total": {"type": "integer", "description": "Total number of maintenance issues"}, "open": {"type": "integer", "description": "Number of open issues"}, "in_progress": {"type": "integer", "description": "Number of issues in progress"}, "critical": {"type": "integer", "description": "Number of critical issues"}}, "required": ["total", "open", "in_progress", "critical"]}, "recent_alerts": {"type": "array", "items": {"$ref": "#/components/schemas/DashboardAlert"}, "description": "Recent system alerts"}, "system_health": {"type": "object", "properties": {"api_status": {"type": "string", "enum": ["operational", "degraded", "down"], "description": "API service status"}, "database_status": {"type": "string", "enum": ["operational", "degraded", "down"], "description": "Database service status"}, "last_backup": {"type": "string", "format": "date-time", "description": "Last successful backup timestamp"}, "uptime_percentage": {"type": "number", "minimum": 0, "maximum": 100, "description": "System uptime percentage"}}, "required": ["api_status", "database_status", "uptime_percentage"]}, "quick_stats": {"type": "object", "properties": {"active_users": {"type": "integer", "description": "Number of currently active users"}, "total_notifications": {"type": "integer", "description": "Total unread notifications"}, "pending_approvals": {"type": "integer", "description": "Number of items pending approval"}}, "required": ["active_users", "total_notifications", "pending_approvals"]}}, "required": ["properties", "maintenance_issues", "recent_alerts", "system_health", "quick_stats"]}, "DashboardAlert": {"type": "object", "properties": {"type": {"type": "string", "enum": ["maintenance", "threshold", "system", "user"], "description": "Type of alert"}, "message": {"type": "string", "description": "Alert message"}, "timestamp": {"type": "string", "format": "date-time", "description": "Alert timestamp"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"], "description": "Alert severity level"}, "property_id": {"type": "string", "format": "uuid", "nullable": true, "description": "Related property ID if applicable"}, "related_entity_id": {"type": "string", "format": "uuid", "nullable": true, "description": "Related entity ID if applicable"}}, "required": ["type", "message", "timestamp", "severity"]}}}}