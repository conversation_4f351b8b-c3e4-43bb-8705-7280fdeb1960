#!/usr/bin/env node

/**
 * Test Script for New Endpoints
 * 
 * This script tests all the newly implemented endpoints to ensure they're working correctly.
 */

const fetch = require('node-fetch');

// Configuration
const BASE_URL = 'http://localhost:3001';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'admin123'
};

let authToken = null;

/**
 * Helper function to make authenticated requests
 */
async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  if (authToken) {
    headers.Authorization = `Bearer ${authToken}`;
  }

  try {
    const response = await fetch(url, {
      ...options,
      headers,
    });

    const data = await response.json();
    return {
      status: response.status,
      ok: response.ok,
      data,
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message,
    };
  }
}

/**
 * Authenticate and get token
 */
async function authenticate() {
  console.log('🔐 Authenticating...');
  
  const result = await makeRequest('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify(TEST_USER),
  });

  if (result.ok && result.data.data?.token) {
    authToken = result.data.data.token;
    console.log('✅ Authentication successful');
    return true;
  } else {
    console.log('❌ Authentication failed:', result.data?.message || result.error);
    return false;
  }
}

/**
 * Test logout endpoint
 */
async function testLogout() {
  console.log('\n📤 Testing logout endpoint...');
  
  const result = await makeRequest('/api/auth/logout', {
    method: 'POST',
  });

  if (result.ok) {
    console.log('✅ Logout endpoint working');
  } else {
    console.log('❌ Logout endpoint failed:', result.data?.message || result.error);
  }
}

/**
 * Test mark all notifications read
 */
async function testMarkAllNotificationsRead() {
  console.log('\n📬 Testing mark all notifications read...');
  
  const result = await makeRequest('/api/notifications/mark-all-read', {
    method: 'PATCH',
  });

  if (result.ok) {
    console.log('✅ Mark all notifications read working');
    console.log(`   Marked ${result.data.data?.count || 0} notifications as read`);
  } else {
    console.log('❌ Mark all notifications read failed:', result.data?.message || result.error);
  }
}

/**
 * Test bulk threshold operations
 */
async function testBulkThresholds() {
  console.log('\n⚙️ Testing bulk threshold operations...');
  
  const testThresholds = [
    {
      functional_area: 'fuel',
      property_type: 'residential',
      metric_name: 'fuel_level',
      threshold_type: 'min',
      min_value: 20,
      unit: 'liters',
      severity: 'medium',
    },
    {
      functional_area: 'attendance',
      property_type: 'office',
      metric_name: 'attendance_rate',
      threshold_type: 'min',
      min_value: 80,
      unit: 'percentage',
      severity: 'high',
    },
  ];

  const result = await makeRequest('/api/thresholds/bulk', {
    method: 'POST',
    body: JSON.stringify({ thresholds: testThresholds }),
  });

  if (result.ok) {
    console.log('✅ Bulk threshold creation working');
    console.log(`   Created ${result.data.data?.created?.length || 0} thresholds`);
    console.log(`   Errors: ${result.data.data?.errors?.length || 0}`);
  } else {
    console.log('❌ Bulk threshold creation failed:', result.data?.message || result.error);
  }
}

/**
 * Test bulk user approval
 */
async function testBulkUserApproval() {
  console.log('\n👥 Testing bulk user approval...');
  
  // First, let's try to get some users
  const usersResult = await makeRequest('/api/users?limit=5');
  
  if (!usersResult.ok) {
    console.log('❌ Could not fetch users for testing');
    return;
  }

  const users = usersResult.data.data || [];
  if (users.length === 0) {
    console.log('ℹ️ No users found for bulk approval testing');
    return;
  }

  // Test with empty array (should work but do nothing)
  const result = await makeRequest('/api/users/bulk-approve', {
    method: 'POST',
    body: JSON.stringify({ 
      user_ids: [], // Empty array for testing
      approve: true 
    }),
  });

  if (result.status === 400) {
    console.log('✅ Bulk user approval validation working (empty array rejected)');
  } else {
    console.log('❌ Bulk user approval validation failed');
  }
}

/**
 * Test bulk attendance
 */
async function testBulkAttendance() {
  console.log('\n📊 Testing bulk attendance...');
  
  // Get properties first
  const propertiesResult = await makeRequest('/api/properties?limit=1');
  
  if (!propertiesResult.ok || !propertiesResult.data.data?.length) {
    console.log('ℹ️ No properties found for attendance testing');
    return;
  }

  const property = propertiesResult.data.data[0];
  
  // Get users
  const usersResult = await makeRequest('/api/users?limit=1');
  
  if (!usersResult.ok || !usersResult.data.data?.length) {
    console.log('ℹ️ No users found for attendance testing');
    return;
  }

  const user = usersResult.data.data[0];

  const testRecords = [
    {
      user_id: user.id,
      property_id: property.id,
      date: new Date().toISOString().split('T')[0],
      status: 'present',
      hours_worked: 8,
      notes: 'Test attendance record',
    },
  ];

  const result = await makeRequest('/api/attendance/bulk', {
    method: 'POST',
    body: JSON.stringify({ records: testRecords }),
  });

  if (result.ok) {
    console.log('✅ Bulk attendance working');
    console.log(`   Created: ${result.data.data?.created?.length || 0}`);
    console.log(`   Updated: ${result.data.data?.updated?.length || 0}`);
    console.log(`   Errors: ${result.data.data?.errors?.length || 0}`);
  } else {
    console.log('❌ Bulk attendance failed:', result.data?.message || result.error);
  }
}

/**
 * Test SSE endpoint (just check if it's accessible)
 */
async function testSSE() {
  console.log('\n📡 Testing SSE endpoint...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/notifications/sse`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Accept': 'text/event-stream',
      },
    });

    if (response.ok) {
      console.log('✅ SSE endpoint accessible');
      // Don't actually read the stream in this test
    } else {
      console.log('❌ SSE endpoint failed:', response.status);
    }
  } catch (error) {
    console.log('❌ SSE endpoint error:', error.message);
  }
}

/**
 * Test monitoring endpoints
 */
async function testMonitoring() {
  console.log('\n📈 Testing monitoring endpoints...');
  
  // Get a property for testing
  const propertiesResult = await makeRequest('/api/properties?limit=1');
  
  if (!propertiesResult.ok || !propertiesResult.data.data?.length) {
    console.log('ℹ️ No properties found for monitoring testing');
    return;
  }

  const property = propertiesResult.data.data[0];

  // Test single monitoring
  const singleResult = await makeRequest('/api/monitoring', {
    method: 'POST',
    body: JSON.stringify({
      property_id: property.id,
      service_type: 'fuel',
      metric_name: 'fuel_level',
      value: 75.5,
      unit: 'liters',
    }),
  });

  if (singleResult.ok) {
    console.log('✅ Single monitoring endpoint working');
  } else {
    console.log('❌ Single monitoring failed:', singleResult.data?.message || singleResult.error);
  }

  // Test multiple monitoring
  const multipleResult = await makeRequest('/api/monitoring/multiple', {
    method: 'POST',
    body: JSON.stringify({
      property_id: property.id,
      metrics: [
        {
          service_type: 'fuel',
          metric_name: 'fuel_level',
          value: 80.0,
          unit: 'liters',
        },
        {
          service_type: 'electricity',
          metric_name: 'power_status',
          value: 1,
          unit: 'boolean',
        },
      ],
    }),
  });

  if (multipleResult.ok) {
    console.log('✅ Multiple monitoring endpoint working');
  } else {
    console.log('❌ Multiple monitoring failed:', multipleResult.data?.message || multipleResult.error);
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 Starting endpoint tests...\n');

  // Authenticate first
  const authenticated = await authenticate();
  if (!authenticated) {
    console.log('❌ Cannot proceed without authentication');
    return;
  }

  // Run all tests
  await testLogout();
  await testMarkAllNotificationsRead();
  await testBulkThresholds();
  await testBulkUserApproval();
  await testBulkAttendance();
  await testSSE();
  await testMonitoring();

  console.log('\n🎉 All tests completed!');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  makeRequest,
  authenticate,
};
