{"name": "srsr-property-management-backend", "version": "1.0.0", "description": "SRSR Property Management Backend API with NextJS and PostgreSQL", "main": "index.js", "scripts": {"dev": "next dev --hostname 0.0.0.0", "dev:local": "next dev", "build": "next build", "start": "next start --hostname 0.0.0.0", "lint": "next lint", "setup": "node scripts/setup.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "node prisma/seed.js", "db:seed:full": "node prisma/comprehensive-seed.js", "db:reset": "prisma db push --force-reset && npm run db:seed", "db:reset:full": "prisma db push --force-reset && npm run db:seed:full", "reset-passwords": "node scripts/reset-passwords.js", "reset-user-password": "node scripts/reset-user-password.js"}, "dependencies": {"@prisma/client": "^5.7.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "date-fns": "^2.30.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "next": "^14.0.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.0.0", "@types/react": "19.1.6", "@types/uuid": "^9.0.7", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "prisma": "^5.7.0", "typescript": "^5.0.0"}, "keywords": ["nextjs", "postgresql", "prisma", "property-management", "api"], "author": "SRSR Property Management", "license": "MIT"}