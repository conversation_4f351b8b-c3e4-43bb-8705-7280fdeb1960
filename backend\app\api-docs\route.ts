import { NextRequest } from 'next/server';
import { readFileSync } from 'fs';
import { join } from 'path';
import { createApiResponse, corsHeaders } from '@/lib/utils';

export async function GET(request: NextRequest) {
  // Read swagger.json from the project root
  const swaggerPath = join(process.cwd(), 'swagger.json');
  let swaggerDoc;
  try {
    swaggerDoc = readFileSync(swaggerPath, 'utf-8');
  } catch (e) {
    return Response.json(
      createApiResponse({ error: 'Swagger documentation not found.' }),
      { status: 404, headers: corsHeaders() }
    );
  }
  return new Response(swaggerDoc, {
    status: 200,
    headers: {
      ...corsHeaders(),
      'Content-Type': 'application/json',
    },
  });
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
