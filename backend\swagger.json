{"openapi": "3.0.3", "info": {"title": "SRSR Property Management API", "description": "Backend API server for SRSR Property Management System with NextJS, PostgreSQL, and comprehensive role-based access control", "version": "1.0.0", "contact": {"name": "SRSR Property Management", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.srsr.com", "description": "Production server"}], "security": [{"bearerAuth": []}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}, "error": {"type": "string"}, "code": {"type": "string"}}, "required": ["success"]}, "PaginationResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "limit": {"type": "integer"}, "total": {"type": "integer"}, "pages": {"type": "integer"}, "has_next": {"type": "boolean"}, "has_prev": {"type": "boolean"}}}}}, "User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "email": {"type": "string", "format": "email"}, "username": {"type": "string", "nullable": true}, "full_name": {"type": "string"}, "phone": {"type": "string", "nullable": true}, "is_active": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "string"}}, "created_at": {"type": "string", "format": "date-time"}}}, "LoginRequest": {"type": "object", "properties": {"identifier": {"type": "string", "description": "Email, username, or phone number"}, "password": {"type": "string", "minLength": 6}, "login_type": {"type": "string", "enum": ["email", "username", "phone"], "description": "Optional login type hint"}}, "required": ["identifier", "password"]}, "LegacyLoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 6}}, "required": ["email", "password"]}, "RegisterRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "username": {"type": "string", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z0-9_-]+$"}, "password": {"type": "string", "minLength": 6}, "full_name": {"type": "string", "minLength": 2}, "phone": {"type": "string", "pattern": "^[+]?[1-9]\\d{1,14}$"}}, "required": ["email", "password", "full_name"]}, "CreateUserRequest": {"type": "object", "properties": {"full_name": {"type": "string", "minLength": 2}, "email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 6}, "phone": {"type": "string", "pattern": "^[+]?[1-9]\\d{1,14}$"}, "roles": {"type": "array", "items": {"type": "string"}}}, "required": ["full_name", "email", "password"]}, "Property": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "type": {"type": "string", "enum": ["residential", "office", "construction_site"]}, "parent_property_id": {"type": "string", "format": "uuid", "nullable": true}, "address": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "capacity": {"type": "integer", "minimum": 1, "nullable": true, "description": "Office-specific field"}, "department": {"type": "string", "nullable": true, "description": "Office-specific field"}, "project_type": {"type": "string", "nullable": true, "description": "Construction site-specific field"}, "start_date": {"type": "string", "format": "date", "nullable": true, "description": "Construction site-specific field"}, "expected_end_date": {"type": "string", "format": "date", "nullable": true, "description": "Construction site-specific field"}, "hourly_rate_standard": {"type": "number", "minimum": 0, "nullable": true, "description": "Construction site-specific field"}, "is_active": {"type": "boolean"}, "created_at": {"type": "string", "format": "date-time"}}}, "CreatePropertyRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2}, "type": {"type": "string", "enum": ["residential", "office", "construction_site"]}, "parent_property_id": {"type": "string", "format": "uuid"}, "address": {"type": "string"}, "description": {"type": "string"}, "location": {"type": "string"}, "capacity": {"type": "integer", "minimum": 1, "description": "Required for office type"}, "department": {"type": "string", "description": "Optional for office type"}, "project_type": {"type": "string", "description": "Required for construction_site type"}, "start_date": {"type": "string", "format": "date", "description": "Required for construction_site type"}, "expected_end_date": {"type": "string", "format": "date", "description": "Required for construction_site type"}, "hourly_rate_standard": {"type": "number", "minimum": 0, "description": "Optional for construction_site type"}}, "required": ["name", "type"]}}}, "paths": {"/api": {"get": {"summary": "Get API information", "description": "Returns API information, available endpoints, and system status", "tags": ["System"], "security": [], "responses": {"200": {"description": "API information retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/auth/login": {"post": {"summary": "User login", "description": "Authenticate user with email/username/phone and password. Supports multi-field login.", "tags": ["Authentication"], "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/LoginRequest"}, {"$ref": "#/components/schemas/LegacyLoginRequest"}]}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"token": {"type": "string", "description": "JWT authentication token"}, "user": {"$ref": "#/components/schemas/User"}}}}}]}}}}, "401": {"description": "Invalid credentials or account deactivated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/auth/register": {"post": {"summary": "Register new user", "description": "Register a new user account. Only admin users can register new users.", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"201": {"description": "User registered successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"message": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}}}}]}}}}, "400": {"description": "Validation error or user already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/auth/me": {"get": {"summary": "Get current user profile", "description": "Get the profile information of the currently authenticated user", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "User profile retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/User"}}}]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/users": {"get": {"summary": "Get all users", "description": "Retrieve a paginated list of all users with their roles", "tags": ["Users"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "search", "in": "query", "description": "Search term for filtering users by name or email", "required": false, "schema": {"type": "string"}}, {"name": "role", "in": "query", "description": "Filter users by role", "required": false, "schema": {"type": "string"}}, {"name": "is_active", "in": "query", "description": "Filter users by active status", "required": false, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Users retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create new user", "description": "Create a new user account. Only admin users can create new users.", "tags": ["Users"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}}}, "responses": {"201": {"description": "User created successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"message": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}}}}]}}}}, "400": {"description": "Validation error or user already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/users/{id}": {"get": {"summary": "Get user by ID", "description": "Retrieve a specific user by their ID", "tags": ["Users"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/User"}}}]}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "put": {"summary": "Update user", "description": "Update a user's information. Only admin users can update users.", "tags": ["Users"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"full_name": {"type": "string", "minLength": 2}, "email": {"type": "string", "format": "email"}, "phone": {"type": "string", "pattern": "^[+]?[1-9]\\d{1,14}$"}, "is_active": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "User updated successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/User"}}}]}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "delete": {"summary": "Delete user", "description": "Delete a user account. Only admin users can delete users.", "tags": ["Users"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/properties": {"get": {"summary": "Get all properties", "description": "Retrieve a paginated list of properties with optional filtering by type", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "type", "in": "query", "description": "Filter properties by type", "required": false, "schema": {"type": "string", "enum": ["residential", "office", "construction_site"]}}, {"name": "search", "in": "query", "description": "Search term for filtering properties by name or address", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Properties retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create new property", "description": "Create a new property (residential, office, or construction site). Requires admin or property_manager role.", "tags": ["Properties"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePropertyRequest"}}}}, "responses": {"201": {"description": "Property created successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Property"}}}]}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin or property_manager role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/properties/{id}": {"get": {"summary": "Get property by ID", "description": "Retrieve a specific property by its ID with all related information", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Property retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Property"}}}]}}}}, "404": {"description": "Property not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "put": {"summary": "Update property", "description": "Update a property's information. Requires admin or property_manager role.", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePropertyRequest"}}}}, "responses": {"200": {"description": "Property updated successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Property"}}}]}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Property not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin or property_manager role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/roles": {"get": {"summary": "Get all roles", "description": "Retrieve all available roles in the system", "tags": ["Roles"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Roles retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "is_system_role": {"type": "boolean"}, "created_at": {"type": "string", "format": "date-time"}}}}}}]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create new role", "description": "Create a new role. Only admin users can create roles.", "tags": ["Roles"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2}, "description": {"type": "string"}}, "required": ["name"]}}}}, "responses": {"201": {"description": "Role created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error or role already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/permissions": {"get": {"summary": "Get all permissions", "description": "Retrieve all available permissions in the system", "tags": ["Permissions"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Permissions retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "resource": {"type": "string"}, "action": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}}}}}]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create permissions", "description": "Create new permissions (supports bulk creation). Only admin users can create permissions.", "tags": ["Permissions"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"oneOf": [{"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "resource": {"type": "string"}, "action": {"type": "string"}}, "required": ["name", "resource", "action"]}, {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "resource": {"type": "string"}, "action": {"type": "string"}}, "required": ["name", "resource", "action"]}}]}}}}, "responses": {"201": {"description": "Permissions created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/admin/screens": {"get": {"summary": "Get all screen configurations", "description": "Retrieve all screen configurations for permission management", "tags": ["Admin"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Screen configurations retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create screen configuration", "description": "Create a new screen configuration for permission management", "tags": ["Admin"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "title": {"type": "string"}, "route": {"type": "string"}, "required_permissions": {"type": "array", "items": {"type": "string"}}, "allowed_roles": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "title", "route"]}}}}, "responses": {"201": {"description": "Screen configuration created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/admin/widgets": {"get": {"summary": "Get all widget configurations", "description": "Retrieve all widget configurations for permission management", "tags": ["Admin"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Widget configurations retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create widget configuration", "description": "Create a new widget configuration for permission management", "tags": ["Admin"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "screen_name": {"type": "string"}, "widget_type": {"type": "string"}, "required_permissions": {"type": "array", "items": {"type": "string"}}, "allowed_roles": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "screen_name", "widget_type"]}}}}, "responses": {"201": {"description": "Widget configuration created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/maintenance": {"get": {"summary": "Get maintenance issues", "description": "Retrieve a paginated list of maintenance issues with filtering options", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "property_id", "in": "query", "description": "Filter by property ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "status", "in": "query", "description": "Filter by status", "required": false, "schema": {"type": "string", "enum": ["open", "in_progress", "resolved", "closed"]}}, {"name": "priority", "in": "query", "description": "Filter by priority", "required": false, "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "assigned_to", "in": "query", "description": "Filter by assigned user ID", "required": false, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Maintenance issues retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create maintenance issue", "description": "Create a new maintenance issue", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"property_id": {"type": "string", "format": "uuid"}, "title": {"type": "string", "minLength": 3}, "description": {"type": "string", "minLength": 10}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"], "default": "medium"}, "service_type": {"type": "string", "enum": ["electricity", "water", "internet", "security", "hvac", "plumbing", "general"]}, "department": {"type": "string"}, "due_date": {"type": "string", "format": "date"}, "estimated_cost": {"type": "number", "minimum": 0}, "location_details": {"type": "string"}}, "required": ["property_id", "title", "description"]}}}}, "responses": {"201": {"description": "Maintenance issue created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/maintenance/{id}": {"get": {"summary": "Get maintenance issue by ID", "description": "Retrieve a specific maintenance issue with full details", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Maintenance issue retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Maintenance issue not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "put": {"summary": "Update maintenance issue", "description": "Update a maintenance issue's information", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string", "minLength": 3}, "description": {"type": "string", "minLength": 10}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "status": {"type": "string", "enum": ["open", "in_progress", "resolved", "closed"]}, "service_type": {"type": "string", "enum": ["electricity", "water", "internet", "security", "hvac", "plumbing", "general"]}, "department": {"type": "string"}, "due_date": {"type": "string", "format": "date"}, "estimated_cost": {"type": "number", "minimum": 0}, "actual_cost": {"type": "number", "minimum": 0}, "location_details": {"type": "string"}, "resolution_notes": {"type": "string"}}}}}}, "responses": {"200": {"description": "Maintenance issue updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Maintenance issue not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "patch": {"summary": "Update maintenance issue status", "description": "Update only the status of a maintenance issue", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["open", "in_progress", "resolved", "closed"]}, "resolution_notes": {"type": "string"}}, "required": ["status"]}}}}, "responses": {"200": {"description": "Maintenance issue status updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Maintenance issue not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "delete": {"summary": "Delete maintenance issue", "description": "Delete a maintenance issue", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Maintenance issue deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Maintenance issue not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/maintenance/{id}/assign": {"post": {"summary": "Assign maintenance issue", "description": "Assign a maintenance issue to a user", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"assigned_to": {"type": "string", "format": "uuid", "description": "User ID to assign the issue to"}, "assignment_notes": {"type": "string", "description": "Notes about the assignment"}, "due_date": {"type": "string", "format": "date", "description": "Due date for completion"}}, "required": ["assigned_to"]}}}}, "responses": {"200": {"description": "Maintenance issue assigned successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error or invalid assignment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Maintenance issue or user not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/maintenance/{id}/escalate": {"post": {"summary": "Escalate maintenance issue", "description": "Escalate a maintenance issue to higher priority or management", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"escalation_reason": {"type": "string", "minLength": 5, "description": "Reason for escalation"}, "escalation_level": {"type": "integer", "minimum": 1, "maximum": 5, "description": "Escalation level (1-5)"}}}}}}, "responses": {"200": {"description": "Maintenance issue escalated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Maintenance issue not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/properties/{id}/members": {"get": {"summary": "Get property members", "description": "Retrieve all members assigned to a property", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Property members retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Property not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Add property member", "description": "Add a new member to a property", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "string", "format": "uuid"}, "role": {"type": "string", "description": "Role at the property (e.g., office_manager, site_supervisor, construction_worker)"}, "position": {"type": "string", "description": "Job position/title"}, "department": {"type": "string", "description": "Department or team"}, "hourly_rate": {"type": "number", "minimum": 0, "description": "Hourly rate for the member"}, "start_date": {"type": "string", "format": "date", "description": "Start date at the property"}, "end_date": {"type": "string", "format": "date", "description": "End date at the property (optional)"}}, "required": ["user_id", "role"]}}}}, "responses": {"201": {"description": "Property member added successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error or member already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Property or user not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/properties/{id}/attendance": {"get": {"summary": "Get property attendance", "description": "Retrieve attendance records for a property", "tags": ["Attendance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "date", "in": "query", "description": "Filter by specific date", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "start_date", "in": "query", "description": "Filter from start date", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "end_date", "in": "query", "description": "Filter to end date", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "user_id", "in": "query", "description": "Filter by user ID", "required": false, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Attendance records retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Property not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Record property attendance", "description": "Record attendance for property members", "tags": ["Attendance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "attendance": {"type": "array", "items": {"type": "object", "properties": {"worker_id": {"type": "string", "format": "uuid"}, "status": {"type": "string", "enum": ["present", "absent", "late", "half_day"]}, "check_in_time": {"type": "string", "description": "Check-in time (HH:MM format)"}, "check_out_time": {"type": "string", "description": "Check-out time (HH:MM format)"}, "hours_worked": {"type": "number", "minimum": 0, "maximum": 24}, "notes": {"type": "string"}}, "required": ["worker_id", "status"]}}}, "required": ["date", "attendance"]}}}}, "responses": {"201": {"description": "Attendance recorded successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Property not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/generator-fuel/{propertyId}": {"get": {"summary": "Get generator fuel logs", "description": "Retrieve generator fuel logs for a specific property", "tags": ["Generator Fuel"], "security": [{"bearerAuth": []}], "parameters": [{"name": "propertyId", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "start_date", "in": "query", "description": "Filter from start date", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "end_date", "in": "query", "description": "Filter to end date", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Generator fuel logs retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Property not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create generator fuel log", "description": "Create a new generator fuel log entry", "tags": ["Generator Fuel"], "security": [{"bearerAuth": []}], "parameters": [{"name": "propertyId", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"fuel_level_liters": {"type": "number", "minimum": 0, "description": "Current fuel level in liters"}, "consumption_rate": {"type": "number", "minimum": 0, "description": "Fuel consumption rate (liters per hour)"}, "runtime_hours": {"type": "number", "minimum": 0, "description": "Generator runtime hours"}, "efficiency_percentage": {"type": "number", "minimum": 0, "maximum": 100, "description": "Generator efficiency percentage"}, "notes": {"type": "string", "description": "Additional notes about the fuel log"}}, "required": ["fuel_level_liters"]}}}}, "responses": {"201": {"description": "Generator fuel log created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Property not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/dashboard/status": {"get": {"summary": "Get dashboard status", "description": "Retrieve dashboard status and metrics for the system overview", "tags": ["Dashboard"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Dashboard status retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"properties": {"type": "object", "properties": {"total": {"type": "integer"}, "operational": {"type": "integer"}, "warning": {"type": "integer"}, "critical": {"type": "integer"}}}, "maintenance_issues": {"type": "object", "properties": {"total": {"type": "integer"}, "open": {"type": "integer"}, "in_progress": {"type": "integer"}, "critical": {"type": "integer"}}}, "recent_alerts": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string"}, "message": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/thresholds": {"get": {"summary": "Get all thresholds", "description": "Retrieve all threshold configurations for monitoring", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create threshold", "description": "Create a new threshold configuration. Only admin users can create thresholds.", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "Threshold name"}, "description": {"type": "string", "description": "Threshold description"}, "service_type": {"type": "string", "description": "Service type this threshold applies to"}, "metric_name": {"type": "string", "description": "Metric name to monitor"}, "threshold_type": {"type": "string", "enum": ["min", "max", "range"], "description": "Type of threshold"}, "min_value": {"type": "number", "description": "Minimum threshold value"}, "max_value": {"type": "number", "description": "Maximum threshold value"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"], "description": "Alert severity level"}, "is_active": {"type": "boolean", "description": "Whether the threshold is active"}}, "required": ["name", "service_type", "metric_name", "threshold_type", "severity"]}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON><PERSON> created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/thresholds/{id}": {"get": {"summary": "Get threshold by ID", "description": "Retrieve a specific threshold configuration", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Threshold ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "put": {"summary": "Update threshold", "description": "Update a threshold configuration. Only admin users can update thresholds.", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Threshold ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "service_type": {"type": "string"}, "metric_name": {"type": "string"}, "threshold_type": {"type": "string", "enum": ["min", "max", "range"]}, "min_value": {"type": "number"}, "max_value": {"type": "number"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "is_active": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "Threshold updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "delete": {"summary": "Delete threshold", "description": "Delete a threshold configuration. Only admin users can delete thresholds.", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Threshold ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/notifications": {"get": {"summary": "Get notifications", "description": "Retrieve notifications for the current user", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "is_read", "in": "query", "description": "Filter by read status", "required": false, "schema": {"type": "boolean"}}, {"name": "type", "in": "query", "description": "Filter by notification type", "required": false, "schema": {"type": "string", "enum": ["maintenance", "threshold", "system", "user"]}}], "responses": {"200": {"description": "Notifications retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create notification", "description": "Create a new notification. Only admin users can create notifications.", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string", "minLength": 3}, "message": {"type": "string", "minLength": 5}, "type": {"type": "string", "enum": ["maintenance", "threshold", "system", "user"]}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"], "default": "medium"}, "recipient_ids": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "Array of user IDs to send notification to"}, "send_to_all": {"type": "boolean", "description": "Send to all users", "default": false}, "related_entity_id": {"type": "string", "format": "uuid", "description": "ID of related entity (maintenance issue, property, etc.)"}, "related_entity_type": {"type": "string", "description": "Type of related entity"}}, "required": ["title", "message", "type"]}}}}, "responses": {"201": {"description": "Notification created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/notifications/{id}/read": {"patch": {"summary": "Mark notification as read", "description": "Mark a specific notification as read", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Notification ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Notification marked as read successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Notification not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/ott-services/{propertyId}": {"get": {"summary": "Get OTT services for property", "description": "Retrieve OTT service subscriptions for a specific property", "tags": ["OTT Services"], "security": [{"bearerAuth": []}], "parameters": [{"name": "propertyId", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OTT services retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Property not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Add OTT service subscription", "description": "Add a new OTT service subscription for a property", "tags": ["OTT Services"], "security": [{"bearerAuth": []}], "parameters": [{"name": "propertyId", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"service_name": {"type": "string", "description": "Name of the OTT service (e.g., Netflix, Amazon Prime)"}, "subscription_type": {"type": "string", "enum": ["monthly", "yearly", "lifetime"], "description": "Type of subscription"}, "cost": {"type": "number", "minimum": 0, "description": "Subscription cost"}, "start_date": {"type": "string", "format": "date", "description": "Subscription start date"}, "end_date": {"type": "string", "format": "date", "description": "Subscription end date"}, "auto_renewal": {"type": "boolean", "description": "Whether subscription auto-renews", "default": true}, "account_details": {"type": "string", "description": "Account details or credentials"}, "notes": {"type": "string", "description": "Additional notes"}}, "required": ["service_name", "subscription_type", "cost", "start_date"]}}}}, "responses": {"201": {"description": "OTT service subscription added successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Property not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/diesel-additions/{propertyId}": {"get": {"summary": "Get diesel additions for property", "description": "Retrieve diesel fuel addition records for a specific property", "tags": ["Diesel Additions"], "security": [{"bearerAuth": []}], "parameters": [{"name": "propertyId", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "start_date", "in": "query", "description": "Filter from start date", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "end_date", "in": "query", "description": "Filter to end date", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Diesel additions retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Property not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Record diesel addition", "description": "Record a new diesel fuel addition for a property", "tags": ["Diesel Additions"], "security": [{"bearerAuth": []}], "parameters": [{"name": "propertyId", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"quantity_liters": {"type": "number", "minimum": 0, "description": "Quantity of diesel added in liters"}, "cost_per_liter": {"type": "number", "minimum": 0, "description": "Cost per liter of diesel"}, "total_cost": {"type": "number", "minimum": 0, "description": "Total cost of diesel addition"}, "supplier": {"type": "string", "description": "Diesel supplier name"}, "receipt_number": {"type": "string", "description": "Receipt or invoice number"}, "added_by": {"type": "string", "description": "Person who added the diesel"}, "notes": {"type": "string", "description": "Additional notes about the diesel addition"}}, "required": ["quantity_liters", "cost_per_liter", "total_cost"]}}}}, "responses": {"201": {"description": "Diesel addition recorded successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Property not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}}, "tags": [{"name": "System", "description": "System information and health endpoints"}, {"name": "Authentication", "description": "User authentication and authorization"}, {"name": "Users", "description": "User management operations"}, {"name": "Roles", "description": "Role management and assignment"}, {"name": "Permissions", "description": "Permission configuration and management"}, {"name": "Properties", "description": "Property management (residential, office, construction sites)"}, {"name": "Maintenance", "description": "Maintenance issue tracking and management"}, {"name": "Attendance", "description": "Property attendance tracking"}, {"name": "Generator Fuel", "description": "Generator fuel monitoring and logging"}, {"name": "Diesel Additions", "description": "Diesel fuel addition tracking"}, {"name": "OTT Services", "description": "OTT service subscription management"}, {"name": "Uptime Reports", "description": "Service uptime monitoring and reporting"}, {"name": "Function Processes", "description": "Function process management and logging"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Threshold configuration and monitoring"}, {"name": "Notifications", "description": "Notification management and delivery"}, {"name": "Monitoring", "description": "System monitoring and metrics"}, {"name": "Dashboard", "description": "Dashboard data and analytics"}, {"name": "Admin", "description": "Administrative configuration endpoints"}]}