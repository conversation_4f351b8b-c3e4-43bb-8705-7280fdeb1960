{"openapi": "3.0.3", "info": {"title": "SRSR API Schemas", "version": "1.0.0"}, "components": {"schemas": {"User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique user identifier"}, "email": {"type": "string", "format": "email", "description": "User email address"}, "username": {"type": "string", "nullable": true, "description": "Optional username"}, "full_name": {"type": "string", "description": "User's full name"}, "phone": {"type": "string", "nullable": true, "pattern": "^[+]?[1-9]\\d{1,14}$", "description": "User's phone number"}, "is_active": {"type": "boolean", "description": "Whether the user account is active"}, "roles": {"type": "array", "items": {"type": "string"}, "description": "List of user roles"}, "permissions": {"type": "array", "items": {"type": "string"}, "description": "List of user permissions"}, "created_at": {"type": "string", "format": "date-time", "description": "User creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}, "required": ["id", "email", "full_name", "is_active", "roles", "created_at"]}, "Property": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique property identifier"}, "name": {"type": "string", "description": "Property name"}, "type": {"type": "string", "enum": ["residential", "office", "construction_site"], "description": "Type of property"}, "parent_property_id": {"type": "string", "format": "uuid", "nullable": true, "description": "Parent property ID for hierarchical properties"}, "address": {"type": "string", "nullable": true, "description": "Property address"}, "description": {"type": "string", "nullable": true, "description": "Property description"}, "location": {"type": "string", "nullable": true, "description": "Property location details"}, "image_url": {"type": "string", "nullable": true, "description": "Property image URL"}, "capacity": {"type": "integer", "minimum": 1, "nullable": true, "description": "Office capacity (office type only)"}, "department": {"type": "string", "nullable": true, "description": "Department (office type only)"}, "project_type": {"type": "string", "nullable": true, "description": "Project type (construction site only)"}, "start_date": {"type": "string", "format": "date", "nullable": true, "description": "Start date (construction site only)"}, "expected_end_date": {"type": "string", "format": "date", "nullable": true, "description": "Expected end date (construction site only)"}, "hourly_rate_standard": {"type": "number", "minimum": 0, "nullable": true, "description": "Standard hourly rate (construction site only)"}, "is_active": {"type": "boolean", "description": "Whether the property is active"}, "services": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyService"}, "description": "Property services"}, "created_at": {"type": "string", "format": "date-time", "description": "Property creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}, "required": ["id", "name", "type", "is_active", "created_at"]}, "PropertyService": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "service_type": {"type": "string", "enum": ["electricity", "water", "internet", "security", "hvac", "plumbing", "general"]}, "status": {"type": "string", "enum": ["operational", "warning", "critical", "offline"]}, "last_checked": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "required": ["id", "service_type", "status"]}, "MaintenanceIssue": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "property_id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string", "enum": ["open", "in_progress", "resolved", "closed"]}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "service_type": {"type": "string", "enum": ["electricity", "water", "internet", "security", "hvac", "plumbing", "general"]}, "department": {"type": "string", "nullable": true}, "reported_by": {"type": "string", "format": "uuid"}, "assigned_to": {"type": "string", "format": "uuid", "nullable": true}, "due_date": {"type": "string", "format": "date", "nullable": true}, "estimated_cost": {"type": "number", "minimum": 0, "nullable": true}, "actual_cost": {"type": "number", "minimum": 0, "nullable": true}, "location_details": {"type": "string", "nullable": true}, "resolution_notes": {"type": "string", "nullable": true}, "escalation_level": {"type": "integer", "minimum": 0, "maximum": 5, "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "resolved_at": {"type": "string", "format": "date-time", "nullable": true}}, "required": ["id", "property_id", "title", "description", "status", "priority", "reported_by", "created_at"]}, "Role": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "is_system_role": {"type": "boolean", "description": "Whether this is a system-defined role"}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/Permission"}}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "is_system_role", "created_at"]}, "Permission": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "resource": {"type": "string", "description": "Resource this permission applies to"}, "action": {"type": "string", "description": "Action this permission allows"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "resource", "action", "created_at"]}, "Notification": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "message": {"type": "string"}, "type": {"type": "string", "enum": ["maintenance", "threshold", "system", "user"]}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "is_read": {"type": "boolean"}, "recipient_id": {"type": "string", "format": "uuid"}, "related_entity_id": {"type": "string", "format": "uuid", "nullable": true}, "related_entity_type": {"type": "string", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "read_at": {"type": "string", "format": "date-time", "nullable": true}}, "required": ["id", "title", "message", "type", "priority", "is_read", "recipient_id", "created_at"]}}}}