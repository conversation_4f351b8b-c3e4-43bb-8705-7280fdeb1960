import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, handleError, corsHeaders } from '@/lib/utils';

async function markAllReadHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    // Mark all unread notifications for the current user as read
    const result = await prisma.notification.updateMany({
      where: {
        userId: currentUser.id,
        isRead: false,
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });

    return Response.json(
      createApiResponse({
        message: 'All notifications marked as read',
        count: result.count,
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to mark all notifications as read');
  }
}

// PATCH /api/notifications/mark-all-read - Mark all notifications as read
export const PATCH = requireAuth(markAllReadHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
