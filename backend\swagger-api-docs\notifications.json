{"openapi": "3.0.3", "info": {"title": "SRSR API - Notifications", "version": "1.0.0"}, "paths": {"/api/notifications": {"get": {"summary": "Get notifications", "description": "Retrieve notifications for the current user with pagination and filtering", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/LimitParam"}, {"name": "is_read", "in": "query", "description": "Filter by read status", "required": false, "schema": {"type": "boolean"}}, {"name": "type", "in": "query", "description": "Filter by notification type", "required": false, "schema": {"type": "string", "enum": ["maintenance", "threshold", "system", "user"]}}, {"name": "priority", "in": "query", "description": "Filter by priority level", "required": false, "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "unread_only", "in": "query", "description": "Show only unread notifications", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Notifications retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse"}, "example": {"success": true, "data": [{"id": "123e4567-e89b-12d3-a456-426614174000", "title": "Maintenance Issue Assigned", "message": "You have been assigned a new maintenance issue: Power outage in Building A", "type": "maintenance", "priority": "critical", "is_read": false, "related_entity_id": "456e7890-e89b-12d3-a456-426614174001", "related_entity_type": "maintenance_issue", "created_at": "2024-01-15T10:30:00Z"}], "pagination": {"page": 1, "limit": 10, "total": 25, "pages": 3, "has_next": true, "has_prev": false}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "post": {"summary": "Create notification", "description": "Create a new notification. Only admin users can create notifications.", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNotificationRequest"}, "examples": {"system_notification": {"summary": "System notification to all users", "value": {"title": "System Maintenance Scheduled", "message": "System maintenance is scheduled for tonight from 2:00 AM to 4:00 AM. Please save your work.", "type": "system", "priority": "medium", "send_to_all": true}}, "targeted_notification": {"summary": "Notification to specific users", "value": {"title": "New Property Assignment", "message": "You have been assigned to manage the new Sunset Apartments property.", "type": "user", "priority": "medium", "recipient_ids": ["123e4567-e89b-12d3-a456-426614174000"], "related_entity_id": "456e7890-e89b-12d3-a456-426614174001", "related_entity_type": "property"}}}}}}, "responses": {"201": {"description": "Notification created successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"message": {"type": "string"}, "notification_count": {"type": "integer", "description": "Number of notifications created"}}}}}]}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "403": {"$ref": "#/components/responses/ForbiddenError"}}}}, "/api/notifications/{id}/read": {"patch": {"summary": "Mark notification as read", "description": "Mark a specific notification as read", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Notification ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Notification marked as read successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": true, "data": {"message": "Notification marked as read", "read_at": "2024-01-15T11:00:00Z"}}}}}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/notifications/mark-all-read": {"patch": {"summary": "Mark all notifications as read", "description": "Mark all notifications for the current user as read", "tags": ["Notifications"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "All notifications marked as read successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": true, "data": {"message": "All notifications marked as read", "count": 15}}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}}, "components": {"schemas": {"CreateNotificationRequest": {"type": "object", "properties": {"title": {"type": "string", "minLength": 3, "maxLength": 200, "description": "Notification title"}, "message": {"type": "string", "minLength": 5, "maxLength": 1000, "description": "Notification message"}, "type": {"type": "string", "enum": ["maintenance", "threshold", "system", "user"], "description": "Type of notification"}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"], "default": "medium", "description": "Notification priority level"}, "recipient_ids": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "Array of user IDs to send notification to"}, "send_to_all": {"type": "boolean", "description": "Send to all users", "default": false}, "related_entity_id": {"type": "string", "format": "uuid", "description": "ID of related entity (maintenance issue, property, etc.)"}, "related_entity_type": {"type": "string", "description": "Type of related entity"}, "scheduled_for": {"type": "string", "format": "date-time", "description": "Schedule notification for future delivery"}, "expires_at": {"type": "string", "format": "date-time", "description": "Notification expiration time"}}, "required": ["title", "message", "type"], "oneOf": [{"properties": {"send_to_all": {"const": true}}}, {"properties": {"recipient_ids": {"minItems": 1}}, "required": ["recipient_ids"]}]}}}}