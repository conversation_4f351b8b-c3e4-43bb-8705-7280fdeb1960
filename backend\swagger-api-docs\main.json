{"openapi": "3.0.3", "info": {"title": "SRSR Property Management API", "description": "Comprehensive backend API server for SRSR Property Management System with NextJS, PostgreSQL, and role-based access control", "version": "1.0.0", "contact": {"name": "SRSR Property Management", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.srsr.com", "description": "Production server"}, {"url": "https://staging-api.srsr.com", "description": "Staging server"}], "security": [{"bearerAuth": []}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token obtained from /api/auth/login endpoint"}}, "parameters": {"PageParam": {"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, "LimitParam": {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, "SearchParam": {"name": "search", "in": "query", "description": "Search term for filtering", "required": false, "schema": {"type": "string", "minLength": 1}}}, "responses": {"UnauthorizedError": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": false, "error": "Authentication required", "code": "UNAUTHORIZED"}}}}, "ForbiddenError": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": false, "error": "Insufficient permissions", "code": "FORBIDDEN"}}}}, "NotFoundError": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": false, "error": "Resource not found", "code": "NOT_FOUND"}}}}, "ValidationError": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": false, "error": "Validation failed", "code": "VALIDATION_ERROR"}}}}, "InternalServerError": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": false, "error": "Internal server error", "code": "INTERNAL_ERROR"}}}}}, "schemas": {"ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates if the request was successful"}, "data": {"description": "Response data (varies by endpoint)"}, "error": {"type": "string", "description": "Error message if success is false"}, "code": {"type": "string", "description": "Error code for programmatic handling"}}, "required": ["success"], "example": {"success": true, "data": {}}}, "PaginationResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Items per page"}, "total": {"type": "integer", "description": "Total number of items"}, "pages": {"type": "integer", "description": "Total number of pages"}, "has_next": {"type": "boolean", "description": "Whether there is a next page"}, "has_prev": {"type": "boolean", "description": "Whether there is a previous page"}}}}, "required": ["success", "data", "pagination"]}}}, "paths": {"/api": {"get": {"summary": "Get API information", "description": "Returns API information, available endpoints, and system status", "tags": ["System"], "security": [], "responses": {"200": {"description": "API information retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": true, "data": {"name": "SRSR Property Management API", "version": "1.0.0", "status": "operational", "timestamp": "2024-01-15T10:30:00Z"}}}}}}}}}, "tags": [{"name": "System", "description": "System information and health endpoints"}, {"name": "Authentication", "description": "User authentication and authorization"}, {"name": "Users", "description": "User management operations"}, {"name": "Roles", "description": "Role management and assignment"}, {"name": "Permissions", "description": "Permission configuration and management"}, {"name": "Properties", "description": "Property management (residential, office, construction sites)"}, {"name": "Maintenance", "description": "Maintenance issue tracking and management"}, {"name": "Attendance", "description": "Property attendance tracking"}, {"name": "Generator Fuel", "description": "Generator fuel monitoring and logging"}, {"name": "Diesel Additions", "description": "Diesel fuel addition tracking"}, {"name": "OTT Services", "description": "OTT service subscription management"}, {"name": "Uptime Reports", "description": "Service uptime monitoring and reporting"}, {"name": "Function Processes", "description": "Function process management and logging"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Threshold configuration and monitoring"}, {"name": "Notifications", "description": "Notification management and delivery"}, {"name": "Monitoring", "description": "System monitoring and metrics"}, {"name": "Dashboard", "description": "Dashboard data and analytics"}, {"name": "Admin", "description": "Administrative configuration endpoints"}], "externalDocs": {"description": "Find more info about SRSR Property Management", "url": "https://github.com/srsr-property-management/api-docs"}}