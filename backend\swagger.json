{"openapi": "3.0.3", "info": {"title": "SRSR Property Management API", "description": "Backend API server for SRSR Property Management System with NextJS, PostgreSQL, and comprehensive role-based access control", "version": "1.0.0", "contact": {"name": "SRSR Property Management", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.srsr.com", "description": "Production server"}], "security": [{"bearerAuth": []}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}, "error": {"type": "string"}, "code": {"type": "string"}}, "required": ["success"]}, "PaginationResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "limit": {"type": "integer"}, "total": {"type": "integer"}, "pages": {"type": "integer"}, "has_next": {"type": "boolean"}, "has_prev": {"type": "boolean"}}}}}, "User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "email": {"type": "string", "format": "email"}, "username": {"type": "string", "nullable": true}, "full_name": {"type": "string"}, "phone": {"type": "string", "nullable": true}, "is_active": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "string"}}, "created_at": {"type": "string", "format": "date-time"}}}, "LoginRequest": {"type": "object", "properties": {"identifier": {"type": "string", "description": "Email, username, or phone number"}, "password": {"type": "string", "minLength": 6}, "login_type": {"type": "string", "enum": ["email", "username", "phone"], "description": "Optional login type hint"}}, "required": ["identifier", "password"]}, "LegacyLoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 6}}, "required": ["email", "password"]}, "RegisterRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "username": {"type": "string", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z0-9_-]+$"}, "password": {"type": "string", "minLength": 6}, "full_name": {"type": "string", "minLength": 2}, "phone": {"type": "string", "pattern": "^[+]?[1-9]\\d{1,14}$"}}, "required": ["email", "password", "full_name"]}, "CreateUserRequest": {"type": "object", "properties": {"full_name": {"type": "string", "minLength": 2}, "email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 6}, "phone": {"type": "string", "pattern": "^[+]?[1-9]\\d{1,14}$"}, "roles": {"type": "array", "items": {"type": "string"}}}, "required": ["full_name", "email", "password"]}, "Property": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "type": {"type": "string", "enum": ["residential", "office", "construction_site"]}, "parent_property_id": {"type": "string", "format": "uuid", "nullable": true}, "address": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "capacity": {"type": "integer", "minimum": 1, "nullable": true, "description": "Office-specific field"}, "department": {"type": "string", "nullable": true, "description": "Office-specific field"}, "project_type": {"type": "string", "nullable": true, "description": "Construction site-specific field"}, "start_date": {"type": "string", "format": "date", "nullable": true, "description": "Construction site-specific field"}, "expected_end_date": {"type": "string", "format": "date", "nullable": true, "description": "Construction site-specific field"}, "hourly_rate_standard": {"type": "number", "minimum": 0, "nullable": true, "description": "Construction site-specific field"}, "is_active": {"type": "boolean"}, "created_at": {"type": "string", "format": "date-time"}}}, "CreatePropertyRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2}, "type": {"type": "string", "enum": ["residential", "office", "construction_site"]}, "parent_property_id": {"type": "string", "format": "uuid"}, "address": {"type": "string"}, "description": {"type": "string"}, "location": {"type": "string"}, "capacity": {"type": "integer", "minimum": 1, "description": "Required for office type"}, "department": {"type": "string", "description": "Optional for office type"}, "project_type": {"type": "string", "description": "Required for construction_site type"}, "start_date": {"type": "string", "format": "date", "description": "Required for construction_site type"}, "expected_end_date": {"type": "string", "format": "date", "description": "Required for construction_site type"}, "hourly_rate_standard": {"type": "number", "minimum": 0, "description": "Optional for construction_site type"}}, "required": ["name", "type"]}}}, "paths": {"/api": {"get": {"summary": "Get API information", "description": "Returns API information, available endpoints, and system status", "tags": ["System"], "security": [], "responses": {"200": {"description": "API information retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/auth/login": {"post": {"summary": "User login", "description": "Authenticate user with email/username/phone and password. Supports multi-field login.", "tags": ["Authentication"], "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/LoginRequest"}, {"$ref": "#/components/schemas/LegacyLoginRequest"}]}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"token": {"type": "string", "description": "JWT authentication token"}, "user": {"$ref": "#/components/schemas/User"}}}}}]}}}}, "401": {"description": "Invalid credentials or account deactivated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/auth/register": {"post": {"summary": "Register new user", "description": "Register a new user account. Only admin users can register new users.", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"201": {"description": "User registered successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"message": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}}}}]}}}}, "400": {"description": "Validation error or user already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/auth/me": {"get": {"summary": "Get current user profile", "description": "Get the profile information of the currently authenticated user", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "User profile retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/User"}}}]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/users": {"get": {"summary": "Get all users", "description": "Retrieve a paginated list of all users with their roles", "tags": ["Users"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "search", "in": "query", "description": "Search term for filtering users by name or email", "required": false, "schema": {"type": "string"}}, {"name": "role", "in": "query", "description": "Filter users by role", "required": false, "schema": {"type": "string"}}, {"name": "is_active", "in": "query", "description": "Filter users by active status", "required": false, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Users retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create new user", "description": "Create a new user account. Only admin users can create new users.", "tags": ["Users"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}}}, "responses": {"201": {"description": "User created successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"message": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}}}}]}}}}, "400": {"description": "Validation error or user already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/users/{id}": {"get": {"summary": "Get user by ID", "description": "Retrieve a specific user by their ID", "tags": ["Users"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/User"}}}]}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "put": {"summary": "Update user", "description": "Update a user's information. Only admin users can update users.", "tags": ["Users"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"full_name": {"type": "string", "minLength": 2}, "email": {"type": "string", "format": "email"}, "phone": {"type": "string", "pattern": "^[+]?[1-9]\\d{1,14}$"}, "is_active": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "User updated successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/User"}}}]}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "delete": {"summary": "Delete user", "description": "Delete a user account. Only admin users can delete users.", "tags": ["Users"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/properties": {"get": {"summary": "Get all properties", "description": "Retrieve a paginated list of properties with optional filtering by type", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of items per page", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "type", "in": "query", "description": "Filter properties by type", "required": false, "schema": {"type": "string", "enum": ["residential", "office", "construction_site"]}}, {"name": "search", "in": "query", "description": "Search term for filtering properties by name or address", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Properties retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create new property", "description": "Create a new property (residential, office, or construction site). Requires admin or property_manager role.", "tags": ["Properties"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePropertyRequest"}}}}, "responses": {"201": {"description": "Property created successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Property"}}}]}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin or property_manager role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/properties/{id}": {"get": {"summary": "Get property by ID", "description": "Retrieve a specific property by its ID with all related information", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Property retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Property"}}}]}}}}, "404": {"description": "Property not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "put": {"summary": "Update property", "description": "Update a property's information. Requires admin or property_manager role.", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePropertyRequest"}}}}, "responses": {"200": {"description": "Property updated successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Property"}}}]}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Property not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin or property_manager role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/roles": {"get": {"summary": "Get all roles", "description": "Retrieve all available roles in the system", "tags": ["Roles"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Roles retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "is_system_role": {"type": "boolean"}, "created_at": {"type": "string", "format": "date-time"}}}}}}]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create new role", "description": "Create a new role. Only admin users can create roles.", "tags": ["Roles"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2}, "description": {"type": "string"}}, "required": ["name"]}}}}, "responses": {"201": {"description": "Role created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error or role already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/permissions": {"get": {"summary": "Get all permissions", "description": "Retrieve all available permissions in the system", "tags": ["Permissions"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Permissions retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "resource": {"type": "string"}, "action": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}}}}}]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create permissions", "description": "Create new permissions (supports bulk creation). Only admin users can create permissions.", "tags": ["Permissions"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"oneOf": [{"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "resource": {"type": "string"}, "action": {"type": "string"}}, "required": ["name", "resource", "action"]}, {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "resource": {"type": "string"}, "action": {"type": "string"}}, "required": ["name", "resource", "action"]}}]}}}}, "responses": {"201": {"description": "Permissions created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions (admin role required)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/admin/screens": {"get": {"summary": "Get all screen configurations", "description": "Retrieve all screen configurations for permission management", "tags": ["Admin"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Screen configurations retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create screen configuration", "description": "Create a new screen configuration for permission management", "tags": ["Admin"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "title": {"type": "string"}, "route": {"type": "string"}, "required_permissions": {"type": "array", "items": {"type": "string"}}, "allowed_roles": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "title", "route"]}}}}, "responses": {"201": {"description": "Screen configuration created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/admin/widgets": {"get": {"summary": "Get all widget configurations", "description": "Retrieve all widget configurations for permission management", "tags": ["Admin"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Widget configurations retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"summary": "Create widget configuration", "description": "Create a new widget configuration for permission management", "tags": ["Admin"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "screen_name": {"type": "string"}, "widget_type": {"type": "string"}, "required_permissions": {"type": "array", "items": {"type": "string"}}, "allowed_roles": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "screen_name", "widget_type"]}}}}, "responses": {"201": {"description": "Widget configuration created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}}, "tags": [{"name": "System", "description": "System information and health endpoints"}, {"name": "Authentication", "description": "User authentication and authorization"}, {"name": "Users", "description": "User management operations"}, {"name": "Roles", "description": "Role management and assignment"}, {"name": "Permissions", "description": "Permission configuration and management"}, {"name": "Properties", "description": "Property management (residential, office, construction sites)"}, {"name": "Maintenance", "description": "Maintenance issue tracking and management"}, {"name": "Attendance", "description": "Property attendance tracking"}, {"name": "Generator Fuel", "description": "Generator fuel monitoring and logging"}, {"name": "Diesel Additions", "description": "Diesel fuel addition tracking"}, {"name": "OTT Services", "description": "OTT service subscription management"}, {"name": "Uptime Reports", "description": "Service uptime monitoring and reporting"}, {"name": "Function Processes", "description": "Function process management and logging"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Threshold configuration and monitoring"}, {"name": "Notifications", "description": "Notification management and delivery"}, {"name": "Monitoring", "description": "System monitoring and metrics"}, {"name": "Dashboard", "description": "Dashboard data and analytics"}, {"name": "Admin", "description": "Administrative configuration endpoints"}]}