{"openapi": "3.0.3", "info": {"title": "SRSR API - Recently Implemented Endpoints", "description": "Documentation for endpoints that were recently implemented to complete the API", "version": "1.0.0"}, "paths": {"/api/notifications/sse": {"get": {"summary": "Server-Sent Events for notifications", "description": "Real-time notification stream using Server-Sent Events", "tags": ["Notifications", "Missing"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "SSE stream established", "content": {"text/event-stream": {"schema": {"type": "string"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/monitoring": {"post": {"summary": "Submit monitoring data", "description": "Submit single metric monitoring data", "tags": ["Monitoring", "Missing"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"property_id": {"type": "string", "format": "uuid"}, "service_type": {"type": "string"}, "metric_name": {"type": "string"}, "value": {"type": "number"}, "unit": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}, "required": ["property_id", "service_type", "metric_name", "value"]}}}}, "responses": {"201": {"description": "Monitoring data submitted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/monitoring/multiple": {"post": {"summary": "Submit multiple monitoring metrics", "description": "Submit multiple monitoring metrics in a single request", "tags": ["Monitoring", "Missing"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"property_id": {"type": "string", "format": "uuid"}, "metrics": {"type": "array", "items": {"type": "object", "properties": {"service_type": {"type": "string"}, "metric_name": {"type": "string"}, "value": {"type": "number"}, "unit": {"type": "string"}}, "required": ["service_type", "metric_name", "value"]}}}, "required": ["property_id", "metrics"]}}}}, "responses": {"201": {"description": "Multiple monitoring metrics submitted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/permissions/screens": {"get": {"summary": "Get screen permissions", "description": "Get all screen permission configurations", "tags": ["Permissions", "Missing"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Screen permissions retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "post": {"summary": "Create screen permission", "description": "Create a new screen permission configuration", "tags": ["Permissions", "Missing"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"screen_name": {"type": "string"}, "required_permissions": {"type": "array", "items": {"type": "string"}}, "allowed_roles": {"type": "array", "items": {"type": "string"}}}, "required": ["screen_name"]}}}}, "responses": {"201": {"description": "Screen permission created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/permissions/widgets": {"get": {"summary": "Get widget permissions", "description": "Get all widget permission configurations", "tags": ["Permissions", "Missing"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Widget permissions retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/generator-fuel/logs/{logId}": {"get": {"summary": "Get generator fuel log by ID", "description": "Retrieve a specific generator fuel log entry", "tags": ["Generator Fuel", "Missing"], "security": [{"bearerAuth": []}], "parameters": [{"name": "logId", "in": "path", "description": "Generator fuel log ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Generator fuel log retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "put": {"summary": "Update generator fuel log", "description": "Update a generator fuel log entry", "tags": ["Generator Fuel", "Missing"], "security": [{"bearerAuth": []}], "parameters": [{"name": "logId", "in": "path", "description": "Generator fuel log ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"fuel_level_liters": {"type": "number", "minimum": 0}, "consumption_rate": {"type": "number", "minimum": 0}, "runtime_hours": {"type": "number", "minimum": 0}, "efficiency_percentage": {"type": "number", "minimum": 0, "maximum": 100}, "notes": {"type": "string"}}}}}}, "responses": {"200": {"description": "Generator fuel log updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "delete": {"summary": "Delete generator fuel log", "description": "Delete a generator fuel log entry", "tags": ["Generator Fuel", "Missing"], "security": [{"bearerAuth": []}], "parameters": [{"name": "logId", "in": "path", "description": "Generator fuel log ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Generator fuel log deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/users/bulk-approve": {"post": {"summary": "Bulk approve/reject users", "description": "Bulk approve or reject multiple users", "tags": ["Users", "Implemented"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_ids": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "approve": {"type": "boolean", "default": true}, "rejection_reason": {"type": "string"}}, "required": ["user_ids"]}}}}, "responses": {"200": {"description": "Users processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/thresholds/bulk": {"post": {"summary": "Bulk create thresholds", "description": "Create multiple threshold configurations", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Implemented"], "security": [{"bearerAuth": []}], "responses": {"201": {"description": "T<PERSON>esh<PERSON><PERSON> created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "put": {"summary": "Bulk update thresholds", "description": "Update multiple threshold configurations", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Implemented"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Thresholds updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/attendance/bulk": {"post": {"summary": "Bulk create/update attendance", "description": "Create or update multiple attendance records", "tags": ["Attendance", "Implemented"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Attendance records processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/notifications/mark-all-read": {"patch": {"summary": "Mark all notifications as read", "description": "Mark all notifications for the current user as read", "tags": ["Notifications", "Implemented"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "All notifications marked as read", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/auth/logout": {"post": {"summary": "Logout user", "description": "Logout the current user", "tags": ["Authentication", "Implemented"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/users/{id}/roles": {"get": {"summary": "Get user roles", "description": "Get all roles assigned to a specific user", "tags": ["Users", "Implemented"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User roles retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "post": {"summary": "Assign role to user", "description": "Assign a role to a user", "tags": ["Users", "Missing"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"role_id": {"type": "string", "format": "uuid"}}, "required": ["role_id"]}}}}, "responses": {"201": {"description": "Role assigned to user successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}}, "tags": [{"name": "Missing", "description": "Endpoints that are identified but not yet fully implemented"}]}