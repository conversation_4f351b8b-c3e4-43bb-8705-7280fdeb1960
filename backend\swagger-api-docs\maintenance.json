{"openapi": "3.0.3", "info": {"title": "SRSR API - Maintenance", "version": "1.0.0"}, "paths": {"/api/maintenance": {"get": {"summary": "Get maintenance issues", "description": "Retrieve a paginated list of maintenance issues with filtering options", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/LimitParam"}, {"name": "property_id", "in": "query", "description": "Filter by property ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "status", "in": "query", "description": "Filter by status", "required": false, "schema": {"type": "string", "enum": ["open", "in_progress", "resolved", "closed"]}}, {"name": "priority", "in": "query", "description": "Filter by priority", "required": false, "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "assigned_to", "in": "query", "description": "Filter by assigned user ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "service_type", "in": "query", "description": "Filter by service type", "required": false, "schema": {"type": "string", "enum": ["electricity", "water", "internet", "security", "hvac", "plumbing", "general"]}}], "responses": {"200": {"description": "Maintenance issues retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse"}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "post": {"summary": "Create maintenance issue", "description": "Create a new maintenance issue", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMaintenanceIssueRequest"}, "examples": {"electrical_issue": {"summary": "Electrical issue", "value": {"property_id": "123e4567-e89b-12d3-a456-************", "title": "Power outage in Building A", "description": "Complete power outage affecting all units in Building A. Emergency repair needed.", "priority": "critical", "service_type": "electricity", "department": "Maintenance", "due_date": "2024-01-16", "estimated_cost": 500.0, "location_details": "Building A, Main electrical panel"}}, "plumbing_issue": {"summary": "Plumbing issue", "value": {"property_id": "123e4567-e89b-12d3-a456-************", "title": "Leaky faucet in Unit 205", "description": "Kitchen faucet is dripping continuously, causing water waste.", "priority": "medium", "service_type": "plumbing", "estimated_cost": 75.0, "location_details": "Unit 205, Kitchen"}}}}}}, "responses": {"201": {"description": "Maintenance issue created successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/MaintenanceIssue"}}}]}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/maintenance/{id}": {"get": {"summary": "Get maintenance issue by ID", "description": "Retrieve a specific maintenance issue with full details", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Maintenance issue retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/MaintenanceIssue"}}}]}}}}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "put": {"summary": "Update maintenance issue", "description": "Update a maintenance issue's information", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMaintenanceIssueRequest"}}}}, "responses": {"200": {"description": "Maintenance issue updated successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/MaintenanceIssue"}}}]}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "patch": {"summary": "Update maintenance issue status", "description": "Update only the status of a maintenance issue", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMaintenanceStatusRequest"}}}}, "responses": {"200": {"description": "Maintenance issue status updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "delete": {"summary": "Delete maintenance issue", "description": "Delete a maintenance issue", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Maintenance issue deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/maintenance/{id}/assign": {"post": {"summary": "Assign maintenance issue", "description": "Assign a maintenance issue to a user", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignMaintenanceIssueRequest"}}}}, "responses": {"200": {"description": "Maintenance issue assigned successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}, "/api/maintenance/{id}/escalate": {"post": {"summary": "Escalate maintenance issue", "description": "Escalate a maintenance issue to higher priority or management", "tags": ["Maintenance"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Maintenance issue ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EscalateMaintenanceIssueRequest"}}}}, "responses": {"200": {"description": "Maintenance issue escalated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}}, "components": {"schemas": {"CreateMaintenanceIssueRequest": {"type": "object", "properties": {"property_id": {"type": "string", "format": "uuid"}, "title": {"type": "string", "minLength": 3}, "description": {"type": "string", "minLength": 10}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"], "default": "medium"}, "service_type": {"type": "string", "enum": ["electricity", "water", "internet", "security", "hvac", "plumbing", "general"]}, "department": {"type": "string"}, "due_date": {"type": "string", "format": "date"}, "estimated_cost": {"type": "number", "minimum": 0}, "location_details": {"type": "string"}}, "required": ["property_id", "title", "description"]}, "UpdateMaintenanceIssueRequest": {"type": "object", "properties": {"title": {"type": "string", "minLength": 3}, "description": {"type": "string", "minLength": 10}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "status": {"type": "string", "enum": ["open", "in_progress", "resolved", "closed"]}, "service_type": {"type": "string", "enum": ["electricity", "water", "internet", "security", "hvac", "plumbing", "general"]}, "department": {"type": "string"}, "due_date": {"type": "string", "format": "date"}, "estimated_cost": {"type": "number", "minimum": 0}, "actual_cost": {"type": "number", "minimum": 0}, "location_details": {"type": "string"}, "resolution_notes": {"type": "string"}}}, "UpdateMaintenanceStatusRequest": {"type": "object", "properties": {"status": {"type": "string", "enum": ["open", "in_progress", "resolved", "closed"]}, "resolution_notes": {"type": "string"}}, "required": ["status"]}, "AssignMaintenanceIssueRequest": {"type": "object", "properties": {"assigned_to": {"type": "string", "format": "uuid", "description": "User ID to assign the issue to"}, "assignment_notes": {"type": "string", "description": "Notes about the assignment"}, "due_date": {"type": "string", "format": "date", "description": "Due date for completion"}}, "required": ["assigned_to"]}, "EscalateMaintenanceIssueRequest": {"type": "object", "properties": {"escalation_reason": {"type": "string", "minLength": 5, "description": "Reason for escalation"}, "escalation_level": {"type": "integer", "minimum": 1, "maximum": 5, "description": "Escalation level (1-5)"}}}}}}