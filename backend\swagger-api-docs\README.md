# 📚 SRSR Property Management API Documentation

## 🎯 Overview

This folder contains modular OpenAPI 3.0.3 documentation for the SRSR Property Management API, organized by feature areas for better maintainability and clarity.

## 📁 Documentation Structure

### **Core Documentation:**
- `main.json` - Main OpenAPI specification with base configuration
- `schemas.json` - All data models and schema definitions
- `security.json` - Authentication and security configurations

### **Feature-Specific Endpoints:**
- `auth.json` - Authentication and authorization endpoints
- `users.json` - User management endpoints
- `roles-permissions.json` - Role and permission management
- `properties.json` - Property management endpoints
- `maintenance.json` - Maintenance issue management
- `attendance.json` - Attendance tracking endpoints
- `generator-fuel.json` - Generator fuel monitoring
- `dashboard.json` - Dashboard and analytics endpoints
- `thresholds.json` - Threshold configuration and monitoring
- `notifications.json` - Notification management
- `ott-services.json` - OTT service subscription management
- `diesel-additions.json` - Diesel fuel addition tracking
- `uptime-reports.json` - Service uptime monitoring
- `function-processes.json` - Function process management
- `admin.json` - Administrative configuration endpoints
- `monitoring.json` - System monitoring and metrics

### **Additional Documentation:**
- `missing-endpoints.json` - Endpoints identified but not yet implemented
- `legacy-endpoints.json` - Deprecated or legacy endpoint documentation

## 🚀 Usage Instructions

### **1. Combine Documentation:**
```bash
# Use a tool like swagger-merger or manually combine
# Main specification should reference all feature files
```

### **2. Generate Complete Swagger:**
```bash
# Script to merge all JSON files into single swagger.json
node scripts/merge-swagger-docs.js
```

### **3. Serve Documentation:**
```bash
# Serve with Swagger UI
npx swagger-ui-serve swagger-api-docs/main.json

# Or host on backend
# GET /api-docs - serves combined documentation
```

## 📋 Maintenance Guidelines

### **Adding New Endpoints:**
1. Identify the appropriate feature file
2. Add endpoint definition following OpenAPI 3.0.3 format
3. Update schemas.json if new models are needed
4. Test with swagger validator

### **Updating Existing Endpoints:**
1. Locate endpoint in appropriate feature file
2. Update request/response schemas as needed
3. Maintain backward compatibility where possible
4. Document breaking changes

### **Schema Management:**
- All reusable schemas go in `schemas.json`
- Feature-specific schemas can be inline in feature files
- Use `$ref` to reference common schemas

## 🔧 Validation

### **Validate Individual Files:**
```bash
swagger-codegen validate -i swagger-api-docs/auth.json
```

### **Validate Combined Documentation:**
```bash
swagger-codegen validate -i swagger.json
```

## 📈 Benefits

### **✅ Modular Organization:**
- Easy to find and update specific feature documentation
- Parallel development by different team members
- Reduced merge conflicts

### **✅ Maintainability:**
- Smaller, focused files are easier to manage
- Clear separation of concerns
- Easier to review changes

### **✅ Scalability:**
- Easy to add new feature areas
- Can generate feature-specific client SDKs
- Supports microservice architecture evolution

## 🎯 Integration

### **Backend Integration:**
- Update `/api-docs` endpoint to serve combined documentation
- Add validation middleware using schemas
- Generate TypeScript types from schemas

### **Frontend Integration:**
- Generate Dart/TypeScript clients from specific feature docs
- Use for API contract testing
- Reference for frontend development

### **Testing Integration:**
- Use schemas for API response validation
- Generate test data from schemas
- Contract testing between services

This modular approach provides better organization, maintainability, and scalability for the API documentation! 🎉
