{"openapi": "3.0.3", "info": {"title": "SRSR API - Properties", "version": "1.0.0"}, "paths": {"/api/properties": {"get": {"summary": "Get all properties", "description": "Retrieve a paginated list of properties with optional filtering by type", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/LimitParam"}, {"name": "type", "in": "query", "description": "Filter properties by type", "required": false, "schema": {"type": "string", "enum": ["residential", "office", "construction_site"]}}, {"name": "status", "in": "query", "description": "Filter properties by status", "required": false, "schema": {"type": "string", "enum": ["active", "inactive"]}}, {"$ref": "#/components/parameters/SearchParam"}], "responses": {"200": {"description": "Properties retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Property"}}}}]}}}}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "post": {"summary": "Create new property", "description": "Create a new property (residential, office, or construction site). Requires admin or property_manager role.", "tags": ["Properties"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePropertyRequest"}, "examples": {"residential": {"summary": "Residential property", "value": {"name": "Sunset Apartments", "type": "residential", "address": "123 Main Street, City, State", "description": "Modern residential complex"}}, "office": {"summary": "Office property", "value": {"name": "Corporate Headquarters", "type": "office", "address": "456 Business Ave, City, State", "capacity": 100, "department": "IT"}}, "construction_site": {"summary": "Construction site", "value": {"name": "New Mall Construction", "type": "construction_site", "address": "789 Development Rd, City, State", "project_type": "Commercial Building", "start_date": "2024-01-01", "expected_end_date": "2024-12-31", "hourly_rate_standard": 25.5}}}}}}, "responses": {"201": {"description": "Property created successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Property"}}}]}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "403": {"$ref": "#/components/responses/ForbiddenError"}}}}, "/api/properties/{id}": {"get": {"summary": "Get property by ID", "description": "Retrieve a specific property by its ID with all related information", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Property retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Property"}}}]}}}}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "put": {"summary": "Update property", "description": "Update a property's information. Requires admin or property_manager role.", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePropertyRequest"}}}}, "responses": {"200": {"description": "Property updated successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Property"}}}]}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}, "403": {"$ref": "#/components/responses/ForbiddenError"}}}}, "/api/properties/{id}/members": {"get": {"summary": "Get property members", "description": "Retrieve all members assigned to a property", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Property members retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyMember"}}}}]}}}}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}, "post": {"summary": "Add property member", "description": "Add a new member to a property", "tags": ["Properties"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Property ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddPropertyMemberRequest"}}}}, "responses": {"201": {"description": "Property member added successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"$ref": "#/components/responses/ValidationError"}, "404": {"$ref": "#/components/responses/NotFoundError"}, "401": {"$ref": "#/components/responses/UnauthorizedError"}}}}}, "components": {"schemas": {"CreatePropertyRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2, "description": "Property name"}, "type": {"type": "string", "enum": ["residential", "office", "construction_site"], "description": "Type of property"}, "parent_property_id": {"type": "string", "format": "uuid", "description": "Parent property ID for hierarchical properties"}, "address": {"type": "string", "description": "Property address"}, "description": {"type": "string", "description": "Property description"}, "location": {"type": "string", "description": "Property location details"}, "capacity": {"type": "integer", "minimum": 1, "description": "Required for office type"}, "department": {"type": "string", "description": "Optional for office type"}, "project_type": {"type": "string", "description": "Required for construction_site type"}, "start_date": {"type": "string", "format": "date", "description": "Required for construction_site type"}, "expected_end_date": {"type": "string", "format": "date", "description": "Required for construction_site type"}, "hourly_rate_standard": {"type": "number", "minimum": 0, "description": "Optional for construction_site type"}}, "required": ["name", "type"]}, "UpdatePropertyRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2}, "address": {"type": "string"}, "description": {"type": "string"}, "location": {"type": "string"}, "is_active": {"type": "boolean"}, "capacity": {"type": "integer", "minimum": 1}, "department": {"type": "string"}, "project_type": {"type": "string"}, "start_date": {"type": "string", "format": "date"}, "expected_end_date": {"type": "string", "format": "date"}, "hourly_rate_standard": {"type": "number", "minimum": 0}}}, "PropertyMember": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "user_id": {"type": "string", "format": "uuid"}, "property_id": {"type": "string", "format": "uuid"}, "role": {"type": "string"}, "position": {"type": "string"}, "department": {"type": "string"}, "hourly_rate": {"type": "number", "minimum": 0}, "start_date": {"type": "string", "format": "date"}, "end_date": {"type": "string", "format": "date", "nullable": true}, "user": {"$ref": "#/components/schemas/User"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["id", "user_id", "property_id", "role", "created_at"]}, "AddPropertyMemberRequest": {"type": "object", "properties": {"user_id": {"type": "string", "format": "uuid"}, "role": {"type": "string", "description": "Role at the property"}, "position": {"type": "string", "description": "Job position/title"}, "department": {"type": "string", "description": "Department or team"}, "hourly_rate": {"type": "number", "minimum": 0, "description": "Hourly rate for the member"}, "start_date": {"type": "string", "format": "date", "description": "Start date at the property"}, "end_date": {"type": "string", "format": "date", "description": "End date at the property (optional)"}}, "required": ["user_id", "role"]}}}}